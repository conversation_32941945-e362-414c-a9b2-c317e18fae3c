<?php

require_once 'header.php';

?>

<center>
    <h4 class="mt-3 text-danger">Olimpiadas Pedagógicas</h4>            
    <p class="small">Fomentando la excelencia educativa y el desarrollo profesional docente</p>
</center>

<div class="container mt-5">
    <div class="row justify-content-center">
        <!-- Columna izquierda con imagen -->
        <div class="col-md-6">  
            <center>          
                <img src="<?php echo DIRECCION_DEL_SITIO; ?>publico/img/olimpiadas-pedagogicas.png" alt="Olimpiadas Pedagógicas" class="img-fluid rounded shadow-sm" style="max-height: 300px; width: 50%;">
            </center>
            
        </div>
        <!-- Columna derecha con formulario -->
        <div class="col-md-6">
            <h2 class="text-center mb-4">Acceso</h2>
            <?php if (!empty($mensaje)): ?>
                <div class="alert alert-danger text-center"><?php echo $mensaje; ?></div>
            <?php endif; ?>
            <form method="post" action="">
                <div class="mb-3">
                    <label for="email" class="form-label">Correo electrónico</label>
                    <input type="email" class="form-control" id="email" name="email" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">Contraseña</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
                <button type="submit" class="btn btn-danger w-100">Ingresar</button>
            </form>
        </div>
    </div>
</div>
<?php
require_once 'footer.php';
?>