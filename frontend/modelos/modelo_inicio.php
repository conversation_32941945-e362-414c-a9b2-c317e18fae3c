<?php

require_once BASE_PATH . 'backend/modelos/modelo_auditoria_sesiones.php';

$mensaje = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') 
{
    $email    = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';

    // Buscar usuario por email (institucional o alterno) y activo
    $condicion = "(email_institucional = '$email' OR email_alterno = '$email') AND activo = 'S'";
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $sql = "SELECT id, contrasena FROM usuarios WHERE $condicion LIMIT 1";
    $result = $conexion->query($sql);

    if ($row = $result->fetch_assoc()) 
    {
        $hash_en_bd = $row['contrasena'];
        // Verificar la contraseña usando password_verify
        if ($hash_en_bd && password_verify($password, $hash_en_bd))
        {
            $_SESSION['id_usuario']             = $row['id'];
            $_SESSION['usuario_id']             = $row['id']; // Para auditoría
            $_SESSION['usuario_autenticado']    = 'S';

            // Registrar ingreso en auditoría de sesiones
            registrar_ingreso_sesion($row['id'], 'Login exitoso desde formulario de inicio');

            $conexion->close();

            // ir_a_pagina(DIRECCION_DEL_SITIO . '/backend/controladores/controlador_panel.php');

            ir_a_pagina(DIRECCION_DEL_SITIO . '/index.php?url=panel');

            exit;
        } 
        else 
        {
            $mensaje = 'Email o contraseña incorrectos.';
        }
    } 
    else 
    {
        $mensaje = 'Email o contraseña incorrectos.';
    }
    
    $conexion->close();

}

?>