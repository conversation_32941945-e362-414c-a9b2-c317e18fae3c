<?php 

    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);

    define( "TITULO_DEL_SISTEMA" , "OPEDAG-UBE" );

    // Definir la ruta base del proyecto para includes
    define('BASE_PATH', __DIR__ . '/../');

    if ( $_SERVER['HTTP_HOST'] == 'opedag.sited21.net' )
    {
        // ---- BEGIN Acceso a las BDs del Servidor ----

            define( "SERVIDOR"  , "localhost" ) ;

            define( "USUARIO"   , "uegcg0ggc7ooq" ); // ube_admin

            define( "CLAVE"     , "BD_UBE_RJLyaYs4@" ); // Siteground

        // ---- END Acceso a las BDs del Servidor ----


        // ---- BEGIN BDs del Sitio en el Servidor ----
 
        define( "BD_SISTEMA"            , "dbpcvutmh3jdpg" ) ; // ara

        define( "DIRECCION_DEL_SITIO" , "https://opedag.sited21.net/" );

    }
    else 
    {
        // ---- BEGIN Acceso a las BDs del Servidor ----

            define( "SERVIDOR"  , "localhost" ) ;

            define( "USUARIO"   , "root" ); // localhost Laragon

            // define( "CLAVE"     , "bbmrsj01" ); // localhost Laragon JFVM

            define( "CLAVE"     , "" ); // localhost Laragon Otro Lado

        // ---- END Acceso a las BDs del Servidor ----


        // ---- BEGIN BDs del Sitio en el Servidor ----
 
        define( "BD_SISTEMA"            , "opedag" ) ; 

        define( "DIRECCION_DEL_SITIO" , "http://localhost/opedag/" );

    }


    
    define( "MINIMO_NUMERO_DE_CARACTERES_EN_CLAVE_DE_USUARIO"            , 6 ) ;

    define( "MAXIMO_NUMERO_DE_CARACTERES_EN_CLAVE_DE_USUARIO"            , 10 ) ;

?>