<?php

require_once dirname(__DIR__, 2) . '/config/config.php';
require_once 'modelo_auditoria.php';

// Iniciar sesión si no está iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Obtener todas las asignaciones de equipos-estudiantes con información completa
 */
function obtener_equipo_estudiantes() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT
                ee.id,
                ee.equipo_id,
                ee.estudiante_id,
                ee.obs,
                eq.nombre as equipo_nombre,
                CONCAT(COALESCE(u.nombres, ''), ' ', COALESCE(u.apellidos, '')) as estudiante_nombre,
                c.nombre as carrera_nombre,
                ent.nombre as entidad_nombre
            FROM equipo_estudiantes ee
            LEFT JOIN equipos eq ON ee.equipo_id = eq.id
            LEFT JOIN estudiantes est ON ee.estudiante_id = est.id
            LEFT JOIN usuarios u ON est.usuario_id = u.id
            LEFT JOIN carreras c ON est.carrera_id = c.id
            LEFT JOIN entidades ent ON c.entidad_id = ent.id
            WHERE ee.activo = 'S' AND ee.eliminado = 'N'
            ORDER BY eq.nombre, u.apellidos, u.nombres";

    $result = $conexion->query($sql);
    $asignaciones = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $asignaciones[] = $row;
        }
    }

    $conexion->close();
    return $asignaciones;
}

/**
 * Obtener una asignación específica por ID
 */
function obtener_equipo_estudiante_por_id($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $stmt = $conexion->prepare("SELECT
                                    ee.id,
                                    ee.equipo_id,
                                    ee.estudiante_id,
                                    ee.obs
                                FROM equipo_estudiantes ee
                                WHERE ee.id = ? AND ee.activo = 'S' AND ee.eliminado = 'N'
                                LIMIT 1");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $resultado = $stmt->get_result();
    $asignacion = $resultado->fetch_assoc();

    $stmt->close();
    $conexion->close();
    return $asignacion;
}

/**
 * Obtener equipos disponibles para select
 */
function obtener_equipos_para_select() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT id, nombre FROM equipos WHERE activo = 'S' AND eliminado = 'N' ORDER BY nombre";
    $result = $conexion->query($sql);
    $equipos = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $equipos[] = $row;
        }
    }

    $conexion->close();
    return $equipos;
}

/**
 * Obtener estudiantes disponibles para select con información completa
 */
function obtener_estudiantes_para_select() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT
                est.id,
                CONCAT(COALESCE(u.nombres, ''), ' ', COALESCE(u.apellidos, '')) as nombre_completo,
                c.nombre as carrera_nombre,
                ent.nombre as entidad_nombre
            FROM estudiantes est
            LEFT JOIN usuarios u ON est.usuario_id = u.id
            LEFT JOIN carreras c ON est.carrera_id = c.id
            LEFT JOIN entidades ent ON c.entidad_id = ent.id
            WHERE est.activo = 'S' AND est.eliminado = 'N'
            ORDER BY u.apellidos, u.nombres";

    $result = $conexion->query($sql);
    $estudiantes = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $estudiantes[] = $row;
        }
    }

    $conexion->close();
    return $estudiantes;
}

/**
 * Verificar si ya existe una asignación equipo-estudiante
 */
function existe_asignacion_equipo_estudiante($equipo_id, $estudiante_id, $id_excluir = null) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $sql = "SELECT id FROM equipo_estudiantes
            WHERE equipo_id = ? AND estudiante_id = ? AND activo = 'S' AND eliminado = 'N'";

    if ($id_excluir) {
        $sql .= " AND id != ?";
    }

    $stmt = $conexion->prepare($sql);
    if ($id_excluir) {
        $stmt->bind_param("iii", $equipo_id, $estudiante_id, $id_excluir);
    } else {
        $stmt->bind_param("ii", $equipo_id, $estudiante_id);
    }

    $stmt->execute();
    $stmt->store_result();
    $existe = $stmt->num_rows > 0;

    $stmt->close();
    $conexion->close();
    return $existe;
}

/**
 * Crear nueva asignación equipo-estudiante
 */
function crear_equipo_estudiante($datos) {
    // Verificar duplicados
    if (existe_asignacion_equipo_estudiante($datos['equipo_id'], $datos['estudiante_id'])) {
        return 'duplicado';
    }

    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $stmt = $conexion->prepare("INSERT INTO equipo_estudiantes (equipo_id, estudiante_id, obs, activo, eliminado)
                                VALUES (?, ?, ?, 'S', 'N')");
    $stmt->bind_param("iis", $datos['equipo_id'], $datos['estudiante_id'], $datos['obs']);

    try {
        $ok = $stmt->execute();

        // Auditoría: Registrar creación
        if ($ok) {
            $nuevo_id = $conexion->insert_id;
            $datos_nuevos = [
                'equipo_id' => $datos['equipo_id'],
                'estudiante_id' => $datos['estudiante_id'],
                'obs' => $datos['obs'],
                'activo' => 'S',
                'eliminado' => 'N'
            ];
            auditoria_create('equipo_estudiantes', $nuevo_id, $datos_nuevos, 'Asignación equipo-estudiante creada');
        }
    } catch (mysqli_sql_exception $e) {
        $ok = false;
    }

    $stmt->close();
    $conexion->close();
    return $ok;
}

/**
 * Actualizar asignación equipo-estudiante existente
 */
function actualizar_equipo_estudiante($id, $datos) {
    // Verificar duplicados
    if (existe_asignacion_equipo_estudiante($datos['equipo_id'], $datos['estudiante_id'], $id)) {
        return 'duplicado';
    }

    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    // Obtener datos anteriores para auditoría
    $datos_anteriores = obtener_datos_anteriores('equipo_estudiantes', $id);

    $stmt = $conexion->prepare("UPDATE equipo_estudiantes
                                SET equipo_id=?, estudiante_id=?, obs=?
                                WHERE id=? AND activo='S' AND eliminado='N'");
    $stmt->bind_param("iisi", $datos['equipo_id'], $datos['estudiante_id'], $datos['obs'], $id);

    try {
        $ok = $stmt->execute();

        // Auditoría: Registrar actualización
        if ($ok && $datos_anteriores) {
            auditoria_update('equipo_estudiantes', $id, $datos_anteriores, $datos, 'Asignación equipo-estudiante actualizada');
        }
    } catch (mysqli_sql_exception $e) {
        $ok = false;
    }

    $stmt->close();
    $conexion->close();
    return $ok;
}

/**
 * Eliminar asignación equipo-estudiante (eliminación lógica)
 */
function eliminar_equipo_estudiante($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    // Obtener datos anteriores para auditoría
    $datos_anteriores = obtener_datos_anteriores('equipo_estudiantes', $id);

    $stmt = $conexion->prepare("UPDATE equipo_estudiantes
                                SET eliminado='S', activo='N'
                                WHERE id=?");
    $stmt->bind_param("i", $id);

    try {
        $ok = $stmt->execute();

        // Auditoría: Registrar eliminación
        if ($ok && $datos_anteriores) {
            auditoria_delete('equipo_estudiantes', $id, $datos_anteriores, 'Asignación equipo-estudiante eliminada (lógicamente)');
        }
    } catch (mysqli_sql_exception $e) {
        $ok = false;
    }

    $stmt->close();
    $conexion->close();
    return $ok;
}

/**
 * Obtener estudiantes asignados a un equipo específico
 */
function obtener_estudiantes_por_equipo($equipo_id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT
                ee.id,
                ee.obs,
                CONCAT(COALESCE(u.nombres, ''), ' ', COALESCE(u.apellidos, '')) as estudiante_nombre,
                c.nombre as carrera_nombre,
                ent.nombre as entidad_nombre
            FROM equipo_estudiantes ee
            LEFT JOIN estudiantes est ON ee.estudiante_id = est.id
            LEFT JOIN usuarios u ON est.usuario_id = u.id
            LEFT JOIN carreras c ON est.carrera_id = c.id
            LEFT JOIN entidades ent ON c.entidad_id = ent.id
            WHERE ee.equipo_id = ? AND ee.activo = 'S' AND ee.eliminado = 'N'
            ORDER BY u.apellidos, u.nombres";

    $stmt = $conexion->prepare($sql);
    $stmt->bind_param("i", $equipo_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $estudiantes = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $estudiantes[] = $row;
        }
    }

    $stmt->close();
    $conexion->close();
    return $estudiantes;
}

/**
 * Obtener equipos donde está asignado un estudiante específico
 */
function obtener_equipos_por_estudiante($estudiante_id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT
                ee.id,
                ee.obs,
                eq.nombre as equipo_nombre
            FROM equipo_estudiantes ee
            LEFT JOIN equipos eq ON ee.equipo_id = eq.id
            WHERE ee.estudiante_id = ? AND ee.activo = 'S' AND ee.eliminado = 'N'
            ORDER BY eq.nombre";

    $stmt = $conexion->prepare($sql);
    $stmt->bind_param("i", $estudiante_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $equipos = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $equipos[] = $row;
        }
    }

    $stmt->close();
    $conexion->close();
    return $equipos;
}

?>