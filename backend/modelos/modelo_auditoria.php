<?php

require_once dirname(__DIR__, 2) . '/config/config.php';

/**
 * Registrar operación de auditoría
 */
function registrar_auditoria($tabla, $operacion, $registro_id = null, $datos_anteriores = null, $datos_nuevos = null, $obs = null) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    // Obtener información del usuario actual
    $usuario_id = isset($_SESSION['usuario_id']) ? intval($_SESSION['usuario_id']) : null;
    
    // Obtener IP del cliente
    $ip = obtener_ip_cliente();
    
    // Obtener User Agent
    $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
    
    // Preparar datos para inserción
    $tabla = $conexion->real_escape_string($tabla);
    $operacion = $conexion->real_escape_string($operacion);
    $registro_id = $registro_id !== null ? intval($registro_id) : 'NULL';
    $datos_anteriores = $datos_anteriores !== null ? "'" . $conexion->real_escape_string(json_encode($datos_anteriores, JSON_UNESCAPED_UNICODE)) . "'" : 'NULL';
    $datos_nuevos = $datos_nuevos !== null ? "'" . $conexion->real_escape_string(json_encode($datos_nuevos, JSON_UNESCAPED_UNICODE)) . "'" : 'NULL';
    $ip = $conexion->real_escape_string($ip);
    $user_agent = $conexion->real_escape_string($user_agent);
    $obs = $obs !== null ? "'" . $conexion->real_escape_string($obs) . "'" : 'NULL';
    $usuario_id = $usuario_id !== null ? $usuario_id : 'NULL';
    
    $sql = "INSERT INTO auditoria_crud (usuario_id, tabla, operacion, registro_id, datos_anteriores, datos_nuevos, fecha_hora, ip, user_agent, obs, activo, eliminado) 
            VALUES ($usuario_id, '$tabla', '$operacion', $registro_id, $datos_anteriores, $datos_nuevos, NOW(), '$ip', '$user_agent', $obs, 'S', 'N')";
    
    $resultado = $conexion->query($sql);
    $conexion->close();
    
    return $resultado;
}

/**
 * Obtener IP del cliente
 */
function obtener_ip_cliente() {
    $ip = '';
    
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED'];
    } elseif (!empty($_SERVER['HTTP_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['HTTP_FORWARDED'])) {
        $ip = $_SERVER['HTTP_FORWARDED'];
    } elseif (!empty($_SERVER['REMOTE_ADDR'])) {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    
    return $ip;
}

/**
 * Auditoría para operación CREATE
 */
function auditoria_create($tabla, $registro_id, $datos_nuevos, $obs = null) {
    return registrar_auditoria($tabla, 'CREATE', $registro_id, null, $datos_nuevos, $obs);
}

/**
 * Auditoría para operación READ
 */
function auditoria_read($tabla, $registro_id = null, $obs = null) {
    return registrar_auditoria($tabla, 'READ', $registro_id, null, null, $obs);
}

/**
 * Auditoría para operación UPDATE
 */
function auditoria_update($tabla, $registro_id, $datos_anteriores, $datos_nuevos, $obs = null) {
    return registrar_auditoria($tabla, 'UPDATE', $registro_id, $datos_anteriores, $datos_nuevos, $obs);
}

/**
 * Auditoría para operación DELETE
 */
function auditoria_delete($tabla, $registro_id, $datos_anteriores, $obs = null) {
    return registrar_auditoria($tabla, 'DELETE', $registro_id, $datos_anteriores, null, $obs);
}

/**
 * Obtener datos anteriores de un registro antes de modificarlo
 */
function obtener_datos_anteriores($tabla, $id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return null;

    $tabla = $conexion->real_escape_string($tabla);
    $id = intval($id);
    
    $sql = "SELECT * FROM $tabla WHERE id = $id LIMIT 1";
    $result = $conexion->query($sql);
    
    $datos = null;
    if ($result && $result->num_rows > 0) {
        $datos = $result->fetch_assoc();
    }
    
    $conexion->close();
    return $datos;
}

/**
 * Obtener historial de auditoría de un registro específico
 */
function obtener_historial_auditoria($tabla, $registro_id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $tabla = $conexion->real_escape_string($tabla);
    $registro_id = intval($registro_id);
    
    $sql = "SELECT
                a.*,
                CONCAT(COALESCE(u.nombres, ''), ' ', COALESCE(u.apellidos, '')) as usuario_nombre
            FROM auditoria_crud a
            LEFT JOIN usuarios u ON a.usuario_id = u.id
            WHERE a.tabla = '$tabla' AND a.registro_id = $registro_id
            AND a.activo = 'S' AND a.eliminado = 'N'
            ORDER BY a.fecha_hora DESC";
    
    $result = $conexion->query($sql);
    $historial = [];
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $historial[] = $row;
        }
    }
    
    $conexion->close();
    return $historial;
}

/**
 * Obtener estadísticas de auditoría
 */
function obtener_estadisticas_auditoria($fecha_desde = null, $fecha_hasta = null) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $where_fecha = '';
    if ($fecha_desde && $fecha_hasta) {
        $fecha_desde = $conexion->real_escape_string($fecha_desde);
        $fecha_hasta = $conexion->real_escape_string($fecha_hasta);
        $where_fecha = "AND DATE(fecha_hora) BETWEEN '$fecha_desde' AND '$fecha_hasta'";
    }
    
    $sql = "SELECT 
                tabla,
                operacion,
                COUNT(*) as total
            FROM auditoria_crud 
            WHERE activo = 'S' AND eliminado = 'N' $where_fecha
            GROUP BY tabla, operacion
            ORDER BY tabla, operacion";
    
    $result = $conexion->query($sql);
    $estadisticas = [];
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $estadisticas[] = $row;
        }
    }
    
    $conexion->close();
    return $estadisticas;
}
