<?php

require_once dirname(__DIR__, 2) . '/config/config.php';

/**
 * Registrar evento de se<PERSON> (INGRESO o SALIDA)
 */
function registrar_evento_sesion($usuario_id, $tipo_evento, $obs = null) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    // Obtener IP del cliente
    $ip = obtener_ip_cliente_sesion();
    
    // Obtener User Agent
    $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
    
    // Preparar datos para inserción
    $usuario_id = intval($usuario_id);
    $tipo_evento = $conexion->real_escape_string($tipo_evento);
    $ip = $conexion->real_escape_string($ip);
    $user_agent = $conexion->real_escape_string($user_agent);
    $obs = $obs !== null ? "'" . $conexion->real_escape_string($obs) . "'" : 'NULL';
    
    $sql = "INSERT INTO auditoria_sesiones (usuario_id, tipo_evento, fecha_hora, ip, user_agent, obs, activo, eliminado) 
            VALUES ($usuario_id, '$tipo_evento', NOW(), '$ip', '$user_agent', $obs, 'S', 'N')";
    
    $resultado = $conexion->query($sql);
    $conexion->close();
    
    return $resultado;
}

/**
 * Registrar ingreso al sistema
 */
function registrar_ingreso_sesion($usuario_id, $obs = null) {
    return registrar_evento_sesion($usuario_id, 'INGRESO', $obs);
}

/**
 * Registrar salida del sistema
 */
function registrar_salida_sesion($usuario_id, $obs = null) {
    return registrar_evento_sesion($usuario_id, 'SALIDA', $obs);
}

/**
 * Obtener IP del cliente (función específica para sesiones)
 */
function obtener_ip_cliente_sesion() {
    $ip = '';
    
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED'];
    } elseif (!empty($_SERVER['HTTP_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['HTTP_FORWARDED'])) {
        $ip = $_SERVER['HTTP_FORWARDED'];
    } elseif (!empty($_SERVER['REMOTE_ADDR'])) {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    
    return $ip;
}

/**
 * Obtener historial de sesiones de un usuario
 */
function obtener_historial_sesiones_usuario($usuario_id, $limite = 50) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $usuario_id = intval($usuario_id);
    $limite = intval($limite);
    
    $sql = "SELECT 
                s.id,
                s.tipo_evento,
                s.fecha_hora,
                s.ip,
                s.user_agent,
                s.obs,
                CONCAT(COALESCE(u.nombres, ''), ' ', COALESCE(u.apellidos, '')) as usuario_nombre
            FROM auditoria_sesiones s
            LEFT JOIN usuarios u ON s.usuario_id = u.id
            WHERE s.usuario_id = $usuario_id AND s.activo = 'S' AND s.eliminado = 'N'
            ORDER BY s.fecha_hora DESC
            LIMIT $limite";
    
    $result = $conexion->query($sql);
    $historial = [];
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $historial[] = $row;
        }
    }
    
    $conexion->close();
    return $historial;
}

/**
 * Obtener todas las sesiones con filtros
 */
function obtener_sesiones_filtradas($usuario_id = null, $tipo_evento = null, $fecha_desde = null, $fecha_hasta = null, $limite = 1000) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $where_conditions = ["s.activo = 'S'", "s.eliminado = 'N'"];
    
    if ($usuario_id && is_numeric($usuario_id)) {
        $usuario_id = intval($usuario_id);
        $where_conditions[] = "s.usuario_id = $usuario_id";
    }
    
    if ($tipo_evento && in_array($tipo_evento, ['INGRESO', 'SALIDA'])) {
        $tipo_evento = $conexion->real_escape_string($tipo_evento);
        $where_conditions[] = "s.tipo_evento = '$tipo_evento'";
    }
    
    if ($fecha_desde && !empty($fecha_desde)) {
        $fecha_desde = $conexion->real_escape_string($fecha_desde);
        $where_conditions[] = "DATE(s.fecha_hora) >= '$fecha_desde'";
    }
    
    if ($fecha_hasta && !empty($fecha_hasta)) {
        $fecha_hasta = $conexion->real_escape_string($fecha_hasta);
        $where_conditions[] = "DATE(s.fecha_hora) <= '$fecha_hasta'";
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    $limite = intval($limite);
    
    $sql = "SELECT 
                s.id,
                s.usuario_id,
                s.tipo_evento,
                s.fecha_hora,
                s.ip,
                s.user_agent,
                s.obs,
                CONCAT(COALESCE(u.nombres, ''), ' ', COALESCE(u.apellidos, '')) as usuario_nombre
            FROM auditoria_sesiones s
            LEFT JOIN usuarios u ON s.usuario_id = u.id
            WHERE $where_clause
            ORDER BY s.fecha_hora DESC
            LIMIT $limite";
    
    $result = $conexion->query($sql);
    $sesiones = [];
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $sesiones[] = $row;
        }
    }
    
    $conexion->close();
    return $sesiones;
}

/**
 * Obtener estadísticas de sesiones
 */
function obtener_estadisticas_sesiones($fecha_desde = null, $fecha_hasta = null) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $where_fecha = "s.activo = 'S' AND s.eliminado = 'N'";
    if ($fecha_desde && $fecha_hasta) {
        $fecha_desde = $conexion->real_escape_string($fecha_desde);
        $fecha_hasta = $conexion->real_escape_string($fecha_hasta);
        $where_fecha .= " AND DATE(s.fecha_hora) BETWEEN '$fecha_desde' AND '$fecha_hasta'";
    }
    
    $sql = "SELECT 
                s.tipo_evento,
                COUNT(*) as total,
                COUNT(DISTINCT s.usuario_id) as usuarios_unicos
            FROM auditoria_sesiones s 
            WHERE $where_fecha
            GROUP BY s.tipo_evento
            ORDER BY s.tipo_evento";
    
    $result = $conexion->query($sql);
    $estadisticas = [];
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $estadisticas[] = $row;
        }
    }
    
    $conexion->close();
    return $estadisticas;
}

/**
 * Obtener usuarios más activos
 */
function obtener_usuarios_mas_activos($limite = 10, $fecha_desde = null, $fecha_hasta = null) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $where_fecha = "s.activo = 'S' AND s.eliminado = 'N'";
    if ($fecha_desde && $fecha_hasta) {
        $fecha_desde = $conexion->real_escape_string($fecha_desde);
        $fecha_hasta = $conexion->real_escape_string($fecha_hasta);
        $where_fecha .= " AND DATE(s.fecha_hora) BETWEEN '$fecha_desde' AND '$fecha_hasta'";
    }
    
    $limite = intval($limite);
    
    $sql = "SELECT 
                s.usuario_id,
                CONCAT(COALESCE(u.nombres, ''), ' ', COALESCE(u.apellidos, '')) as usuario_nombre,
                COUNT(*) as total_sesiones,
                SUM(CASE WHEN s.tipo_evento = 'INGRESO' THEN 1 ELSE 0 END) as ingresos,
                SUM(CASE WHEN s.tipo_evento = 'SALIDA' THEN 1 ELSE 0 END) as salidas,
                MAX(s.fecha_hora) as ultimo_acceso
            FROM auditoria_sesiones s
            LEFT JOIN usuarios u ON s.usuario_id = u.id
            WHERE $where_fecha
            GROUP BY s.usuario_id, usuario_nombre
            ORDER BY total_sesiones DESC
            LIMIT $limite";
    
    $result = $conexion->query($sql);
    $usuarios = [];
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $usuarios[] = $row;
        }
    }
    
    $conexion->close();
    return $usuarios;
}

?>
