<?php

require_once dirname(__DIR__, 2) . '/config/config.php';

/**
 * Obtener todos los tipos de eventos activos
 */
function obtener_tipos_eventos_en_sistema() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT id, nombre, obs FROM tipos_eventos WHERE activo = 'S' AND eliminado = 'N' ORDER BY id ASC";
    $result = $conexion->query($sql);

    $tipos_eventos = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $tipos_eventos[] = $row;
        }
    }
    $conexion->close();
    return $tipos_eventos;
}

/**
 * Verificar si ya existe un tipo de evento con el mismo nombre
 */
function tipo_evento_existe($nombre, $id_excluir = null) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $nombre = $conexion->real_escape_string($nombre);
    $sql = "SELECT id FROM tipos_eventos WHERE nombre = '$nombre' AND activo = 'S' AND eliminado = 'N'";
    if ($id_excluir !== null) {
        $id_excluir = intval($id_excluir);
        $sql .= " AND id != $id_excluir";
    }
    $result = $conexion->query($sql);
    $existe = ($result && $result->num_rows > 0);
    $conexion->close();
    return $existe;
}

/**
 * Crear nuevo tipo de evento
 */
function crear_tipo_evento($nombre, $obs) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $nombre = $conexion->real_escape_string($nombre);
    $obs = $conexion->real_escape_string($obs ?? '');
    $sql = "INSERT INTO tipos_eventos (nombre, obs, activo, eliminado) VALUES ('$nombre', '$obs', 'S', 'N')";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Actualizar tipo de evento existente
 */
function actualizar_tipo_evento($id, $nombre, $obs) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);
    $nombre = $conexion->real_escape_string($nombre);
    $obs = $conexion->real_escape_string($obs ?? '');
    $sql = "UPDATE tipos_eventos SET nombre = '$nombre', obs = '$obs' WHERE id = $id";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Eliminar tipo de evento (eliminación lógica)
 */
function eliminar_tipo_evento($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);
    $sql = "UPDATE tipos_eventos SET activo = 'N', eliminado = 'S' WHERE id = $id";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Obtener un tipo de evento específico por ID
 */
function obtener_tipo_evento_por_id($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return null;

    $id = intval($id);
    $sql = "SELECT id, nombre, obs FROM tipos_eventos WHERE id = $id AND activo = 'S' AND eliminado = 'N' LIMIT 1";
    $result = $conexion->query($sql);
    
    $tipo_evento = null;
    if ($result && $result->num_rows > 0) {
        $tipo_evento = $result->fetch_assoc();
    }
    
    $conexion->close();
    return $tipo_evento;
}

/**
 * Contar total de tipos de eventos activos
 */
function contar_tipos_eventos_activos() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return 0;

    $sql = "SELECT COUNT(*) as total FROM tipos_eventos WHERE activo = 'S' AND eliminado = 'N'";
    $result = $conexion->query($sql);
    
    $total = 0;
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $total = intval($row['total']);
    }
    
    $conexion->close();
    return $total;
}

/**
 * Verificar si un tipo de evento está siendo usado en otras tablas
 */
function tipo_evento_en_uso($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);
    $en_uso = false;

    // Verificar si existe tabla eventos y la columna correspondiente
    $sql_check_table = "SHOW TABLES LIKE 'eventos'";
    $result_check = $conexion->query($sql_check_table);

    if ($result_check && $result_check->num_rows > 0) {
        // La tabla eventos existe, verificar si tiene la columna tipo_evento
        $sql_check_column = "SHOW COLUMNS FROM eventos LIKE 'tipo_evento'";
        $result_column = $conexion->query($sql_check_column);

        if ($result_column && $result_column->num_rows > 0) {
            // La columna existe, verificar si está en uso
            $sql = "SELECT COUNT(*) as total FROM eventos WHERE tipo_evento = $id AND activo = 'S' AND eliminado = 'N'";
            $result = $conexion->query($sql);

            if ($result && $result->num_rows > 0) {
                $row = $result->fetch_assoc();
                $en_uso = intval($row['total']) > 0;
            }
        }
    }

    $conexion->close();
    return $en_uso;
}

?>
