<?php
require_once dirname(__DIR__, 2) . '/config/config.php';
require_once 'modelo_auditoria.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Obtener todas las asignaturas activas y no eliminadas
 */
function obtener_asignaturas_en_sistema() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT id, nombre, obs FROM asignaturas WHERE activo = 'S' AND eliminado = 'N' ORDER BY nombre ASC";
    $result = $conexion->query($sql);
    $asignaturas = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $asignaturas[] = $row;
        }
    }
    $conexion->close();
    return $asignaturas;
}

/**
 * Crear nueva asignatura
 */
function crear_asignatura($datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $nombre = $conexion->real_escape_string($datos['nombre'] ?? '');
    $obs = $conexion->real_escape_string($datos['obs'] ?? '');

    $sql = "INSERT INTO asignaturas (nombre, activo, eliminado, obs) VALUES ('$nombre', 'S', 'N', '$obs')";
    $ok = $conexion->query($sql);

    // Auditoría: Registrar creación
    if ($ok) {
        $nuevo_id = $conexion->insert_id;
        $datos_nuevos = [
            'nombre' => $datos['nombre'],
            'obs' => $datos['obs'],
            'activo' => 'S',
            'eliminado' => 'N'
        ];
        auditoria_create('asignaturas', $nuevo_id, $datos_nuevos, 'Asignatura creada');
    }

    $conexion->close();
    return $ok;
}

/**
 * Actualizar asignatura existente
 */
function actualizar_asignatura($id, $datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);

    // Obtener datos anteriores para auditoría
    $datos_anteriores = obtener_datos_anteriores('asignaturas', $id);

    $nombre = $conexion->real_escape_string($datos['nombre'] ?? '');
    $obs = $conexion->real_escape_string($datos['obs'] ?? '');

    $sql = "UPDATE asignaturas SET nombre = '$nombre', obs = '$obs' WHERE id = $id";
    $ok = $conexion->query($sql);

    // Auditoría: Registrar actualización
    if ($ok && $datos_anteriores) {
        auditoria_update('asignaturas', $id, $datos_anteriores, $datos, 'Asignatura actualizada');
    }

    $conexion->close();
    return $ok;
}

/**
 * Eliminar asignatura (eliminación lógica)
 */
function eliminar_asignatura($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);

    // Obtener datos anteriores para auditoría
    $datos_anteriores = obtener_datos_anteriores('asignaturas', $id);

    $sql = "UPDATE asignaturas SET activo = 'N', eliminado = 'S' WHERE id = $id";
    $ok = $conexion->query($sql);

    // Auditoría: Registrar eliminación
    if ($ok && $datos_anteriores) {
        auditoria_delete('asignaturas', $id, $datos_anteriores, 'Asignatura eliminada (lógicamente)');
    }

    $conexion->close();
    return $ok;
}

/**
 * Obtener una asignatura específica por ID
 */
function obtener_asignatura_por_id($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return null;
    $id = intval($id);
    $sql = "SELECT id, nombre, obs FROM asignaturas WHERE id = $id AND activo = 'S' AND eliminado = 'N' LIMIT 1";
    $result = $conexion->query($sql);
    $asignatura = null;
    if ($result && $result->num_rows > 0) {
        $asignatura = $result->fetch_assoc();
    }
    $conexion->close();
    return $asignatura;
}