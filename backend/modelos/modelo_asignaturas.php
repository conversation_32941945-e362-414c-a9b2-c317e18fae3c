<?php
require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Obtener todas las asignaturas activas y no eliminadas
 */
function obtener_asignaturas_en_sistema() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT id, nombre, obs FROM asignaturas WHERE activo = 'S' AND eliminado = 'N' ORDER BY nombre ASC";
    $result = $conexion->query($sql);
    $asignaturas = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $asignaturas[] = $row;
        }
    }
    $conexion->close();
    return $asignaturas;
}

/**
 * Crear nueva asignatura
 */
function crear_asignatura($datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $nombre = $conexion->real_escape_string($datos['nombre'] ?? '');
    $obs = $conexion->real_escape_string($datos['obs'] ?? '');

    $sql = "INSERT INTO asignaturas (nombre, activo, eliminado, obs) VALUES ('$nombre', 'S', 'N', '$obs')";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Actualizar asignatura existente
 */
function actualizar_asignatura($id, $datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);
    $nombre = $conexion->real_escape_string($datos['nombre'] ?? '');
    $obs = $conexion->real_escape_string($datos['obs'] ?? '');

    $sql = "UPDATE asignaturas SET nombre = '$nombre', obs = '$obs' WHERE id = $id";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Eliminar asignatura (eliminación lógica)
 */
function eliminar_asignatura($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;
    $id = intval($id);
    $sql = "UPDATE asignaturas SET activo = 'N', eliminado = 'S' WHERE id = $id";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Obtener una asignatura específica por ID
 */
function obtener_asignatura_por_id($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return null;
    $id = intval($id);
    $sql = "SELECT id, nombre, obs FROM asignaturas WHERE id = $id AND activo = 'S' AND eliminado = 'N' LIMIT 1";
    $result = $conexion->query($sql);
    $asignatura = null;
    if ($result && $result->num_rows > 0) {
        $asignatura = $result->fetch_assoc();
    }
    $conexion->close();
    return $asignatura;
}