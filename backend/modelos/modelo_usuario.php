<?php

require_once dirname(__DIR__, 2) . '/config/config.php';
require_once 'modelo_auditoria.php';

function obtener_todos_los_usuarios() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $usuarios = [];
    // Solo usuarios activos y no eliminados
    $sql = "SELECT * FROM usuarios WHERE eliminado = 'N' AND activo = 'S'";
    $result = $conexion->query($sql);
    while ($row = $result->fetch_assoc()) {
        $usuarios[] = $row;
    }
    $conexion->close();
    return $usuarios;
}

function crear_usuario($datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $sexo = (isset($datos['sexo']) && ($datos['sexo'] === 'M' || $datos['sexo'] === 'F')) ? $datos['sexo'] : null;
    $estado_civil = !empty($datos['estado_civil']) ? $datos['estado_civil'] : null;
    $email_institucional = isset($datos['email_institucional']) && trim($datos['email_institucional']) !== '' ? $datos['email_institucional'] : null;
    $email_alterno = isset($datos['email_alterno']) && trim($datos['email_alterno']) !== '' ? $datos['email_alterno'] : null;

    $stmt = $conexion->prepare("INSERT INTO usuarios (apellidos, nombres, email_institucional, email_alterno, sexo, estado_civil, telefono, celular, activo, eliminado) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'S', 'N')");
    $stmt->bind_param(
        "ssssssss",
        $datos['apellidos'],
        $datos['nombres'],
        $email_institucional,
        $email_alterno,
        $sexo,
        $estado_civil,
        $datos['telefono'],
        $datos['celular']
    );
    try {
        $ok = $stmt->execute();
    } catch (mysqli_sql_exception $e) {
        $ok = false;
    }

    // Auditoría: Registrar creación
    if ($ok) {
        $nuevo_id = $conexion->insert_id;
        auditoria_create('usuarios', $nuevo_id, $datos, 'Usuario creado');
    }

    $stmt->close();
    $conexion->close();
    return $ok;
}

function actualizar_usuario($id, $datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);

    // Obtener datos anteriores para auditoría
    $datos_anteriores = obtener_datos_anteriores('usuarios', $id);
    $sexo = (isset($datos['sexo']) && ($datos['sexo'] === 'M' || $datos['sexo'] === 'F')) ? $datos['sexo'] : null;
    $estado_civil = !empty($datos['estado_civil']) ? $datos['estado_civil'] : null;
    $email_institucional = isset($datos['email_institucional']) && trim($datos['email_institucional']) !== '' ? $datos['email_institucional'] : null;
    $email_alterno = isset($datos['email_alterno']) && trim($datos['email_alterno']) !== '' ? $datos['email_alterno'] : null;

    // Solo actualiza si el usuario está activo y no eliminado
    $stmt = $conexion->prepare("UPDATE usuarios SET apellidos=?, nombres=?, email_institucional=?, email_alterno=?, sexo=?, estado_civil=?, telefono=?, celular=? WHERE id=? AND eliminado='N' AND activo='S'");
    $stmt->bind_param(
        "ssssssssi",
        $datos['apellidos'],
        $datos['nombres'],
        $email_institucional,
        $email_alterno,
        $sexo,
        $estado_civil,
        $datos['telefono'],
        $datos['celular'],
        $id
    );
    try {
        $ok = $stmt->execute();
    } catch (mysqli_sql_exception $e) {
        $ok = false;
    }

    // Auditoría: Registrar actualización
    if ($ok && $datos_anteriores) {
        auditoria_update('usuarios', $id, $datos_anteriores, $datos, 'Usuario actualizado');
    }

    $stmt->close();
    $conexion->close();
    return $ok;
}

function eliminar_usuario($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);

    // Obtener datos anteriores para auditoría
    $datos_anteriores = obtener_datos_anteriores('usuarios', $id);

    // Elimina lógicamente: activo='N', eliminado='S'
    $stmt = $conexion->prepare("UPDATE usuarios SET eliminado='S', activo='N' WHERE id=?");
    $stmt->bind_param("i", $id);
    $ok = $stmt->execute();

    // Auditoría: Registrar eliminación
    if ($ok && $datos_anteriores) {
        auditoria_delete('usuarios', $id, $datos_anteriores, 'Usuario eliminado (lógicamente)');
    }

    $stmt->close();
    $conexion->close();
    return $ok;
}

function email_existe($email, $campo, $id_excluir = null) {
    if ($email === null || $email === '') {
        return false;
    }
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    // Solo busca en usuarios activos y no eliminados
    $sql = "SELECT id FROM usuarios WHERE $campo = ? AND eliminado = 'N' AND activo = 'S'";
    if ($id_excluir) {
        $sql .= " AND id != ?";
    }
    $stmt = $conexion->prepare($sql);
    if ($id_excluir) {
        $stmt->bind_param("si", $email, $id_excluir);
    } else {
        $stmt->bind_param("s", $email);
    }
    $stmt->execute();
    $stmt->store_result();
    $existe = $stmt->num_rows > 0;
    $stmt->close();
    $conexion->close();
    return $existe;
}
?>