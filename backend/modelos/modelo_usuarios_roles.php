<?php

require_once dirname(__DIR__, 2) . '/config/config.php';
require_once 'modelo_auditoria.php';

/**
 * Obtener todas las asignaciones de usuarios-roles con información completa
 */
function obtener_usuarios_roles() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT 
                ur.id,
                ur.usuario_id,
                ur.rol_id,
                ur.obs,
                u.apellidos,
                u.nombres,
                u.email_institucional,
                r.nombre as rol_nombre
            FROM roles_por_usuario ur
            INNER JOIN usuarios u ON ur.usuario_id = u.id
            INNER JOIN roles r ON ur.rol_id = r.id
            WHERE ur.activo = 'S' AND ur.eliminado = 'N'
                AND u.activo = 'S' AND u.eliminado = 'N'
                AND r.activo = 'S' AND r.eliminado = 'N'
            ORDER BY u.apellidos ASC, u.nombres ASC, r.nombre ASC";
    
    $result = $conexion->query($sql);
    $usuarios_roles = [];
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $usuarios_roles[] = $row;
        }
    }
    
    $conexion->close();
    return $usuarios_roles;
}

/**
 * Obtener todos los usuarios activos para el select
 */
function obtener_usuarios_para_select() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT id, apellidos, nombres, email_institucional 
            FROM usuarios 
            WHERE activo = 'S' AND eliminado = 'N' 
            ORDER BY apellidos ASC, nombres ASC";
    
    $result = $conexion->query($sql);
    $usuarios = [];
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $usuarios[] = $row;
        }
    }
    
    $conexion->close();
    return $usuarios;
}

/**
 * Obtener todos los roles activos para el select
 */
function obtener_roles_para_select() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT id, nombre 
            FROM roles 
            WHERE activo = 'S' AND eliminado = 'N' 
            ORDER BY nombre ASC";
    
    $result = $conexion->query($sql);
    $roles = [];
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $roles[] = $row;
        }
    }
    
    $conexion->close();
    return $roles;
}

/**
 * Verificar si ya existe una asignación usuario-rol
 */
function usuario_rol_existe($usuario_id, $rol_id, $id_excluir = null) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $usuario_id = intval($usuario_id);
    $rol_id = intval($rol_id);
    
    $sql = "SELECT id FROM roles_por_usuario 
            WHERE usuario_id = $usuario_id AND rol_id = $rol_id 
            AND activo = 'S' AND eliminado = 'N'";
    
    if ($id_excluir !== null) {
        $id_excluir = intval($id_excluir);
        $sql .= " AND id != $id_excluir";
    }
    
    $result = $conexion->query($sql);
    $existe = ($result && $result->num_rows > 0);
    $conexion->close();
    return $existe;
}

/**
 * Crear nueva asignación usuario-rol
 */
function crear_usuario_rol($usuario_id, $rol_id, $obs) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $usuario_id = intval($usuario_id);
    $rol_id = intval($rol_id);
    $obs = $conexion->real_escape_string($obs ?? '');
    
    $sql = "INSERT INTO roles_por_usuario (usuario_id, rol_id, obs, activo, eliminado)
            VALUES ($usuario_id, $rol_id, '$obs', 'S', 'N')";

    $ok = $conexion->query($sql);

    // Auditoría: Registrar creación
    if ($ok) {
        $nuevo_id = $conexion->insert_id;
        $datos_nuevos = ['usuario_id' => $usuario_id, 'rol_id' => $rol_id, 'obs' => $obs, 'activo' => 'S', 'eliminado' => 'N'];
        auditoria_create('roles_por_usuario', $nuevo_id, $datos_nuevos, 'Asignación usuario-rol creada');
    }

    $conexion->close();
    return $ok;
}

/**
 * Actualizar asignación usuario-rol
 */
function actualizar_usuario_rol($id, $usuario_id, $rol_id, $obs) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);

    // Obtener datos anteriores para auditoría
    $datos_anteriores = obtener_datos_anteriores('roles_por_usuario', $id);

    $usuario_id = intval($usuario_id);
    $rol_id = intval($rol_id);
    $obs = $conexion->real_escape_string($obs ?? '');

    $sql = "UPDATE roles_por_usuario
            SET usuario_id = $usuario_id, rol_id = $rol_id, obs = '$obs'
            WHERE id = $id";

    $ok = $conexion->query($sql);

    // Auditoría: Registrar actualización
    if ($ok && $datos_anteriores) {
        $datos_nuevos = ['usuario_id' => $usuario_id, 'rol_id' => $rol_id, 'obs' => $obs];
        auditoria_update('roles_por_usuario', $id, $datos_anteriores, $datos_nuevos, 'Asignación usuario-rol actualizada');
    }

    $conexion->close();
    return $ok;
}

/**
 * Eliminar asignación usuario-rol (eliminación lógica)
 */
function eliminar_usuario_rol($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);

    // Obtener datos anteriores para auditoría
    $datos_anteriores = obtener_datos_anteriores('roles_por_usuario', $id);

    $sql = "UPDATE roles_por_usuario
            SET activo = 'N', eliminado = 'S'
            WHERE id = $id";

    $ok = $conexion->query($sql);

    // Auditoría: Registrar eliminación
    if ($ok && $datos_anteriores) {
        auditoria_delete('roles_por_usuario', $id, $datos_anteriores, 'Asignación usuario-rol eliminada (lógicamente)');
    }

    $conexion->close();
    return $ok;
}

/**
 * Obtener información de una asignación específica
 */
function obtener_usuario_rol_por_id($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return null;

    $id = intval($id);
    $sql = "SELECT 
                ur.id,
                ur.usuario_id,
                ur.rol_id,
                ur.obs,
                u.apellidos,
                u.nombres,
                r.nombre as rol_nombre
            FROM roles_por_usuario ur
            INNER JOIN usuarios u ON ur.usuario_id = u.id
            INNER JOIN roles r ON ur.rol_id = r.id
            WHERE ur.id = $id 
                AND ur.activo = 'S' AND ur.eliminado = 'N'
            LIMIT 1";
    
    $result = $conexion->query($sql);
    $usuario_rol = null;
    
    if ($result && $result->num_rows > 0) {
        $usuario_rol = $result->fetch_assoc();
    }
    
    $conexion->close();
    return $usuario_rol;
}

?>
