<?php
require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Obtener todas las carreras activas y no eliminadas, con nombre de la entidad
 */
function obtener_carreras_en_sistema() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT c.id, c.nombre, c.entidad_id, e.nombre AS entidad_nombre, c.obs
            FROM carreras c
            LEFT JOIN entidades e ON c.entidad_id = e.id
            WHERE c.activo = 'S' AND c.eliminado = 'N'
            ORDER BY c.nombre ASC";
    $result = $conexion->query($sql);
    $carreras = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $carreras[] = $row;
        }
    }
    $conexion->close();
    return $carreras;
}

/**
 * Obtener entidades activas para el select
 */
function obtener_entidades_para_select() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];
    $sql = "SELECT id, nombre FROM entidades WHERE activo = 'S' AND eliminado = 'N' ORDER BY nombre ASC";
    $result = $conexion->query($sql);
    $entidades = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $entidades[] = $row;
        }
    }
    $conexion->close();
    return $entidades;
}

/**
 * Crear nueva carrera
 */
function crear_carrera($datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $nombre = $conexion->real_escape_string($datos['nombre'] ?? '');
    $entidad_id = isset($datos['entidad_id']) && is_numeric($datos['entidad_id']) ? intval($datos['entidad_id']) : 'NULL';
    $obs = $conexion->real_escape_string($datos['obs'] ?? '');

    $sql = "INSERT INTO carreras (nombre, entidad_id, activo, eliminado, obs) VALUES ('$nombre', $entidad_id, 'S', 'N', '$obs')";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Actualizar carrera existente
 */
function actualizar_carrera($id, $datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);
    $nombre = $conexion->real_escape_string($datos['nombre'] ?? '');
    $entidad_id = isset($datos['entidad_id']) && is_numeric($datos['entidad_id']) ? intval($datos['entidad_id']) : 'NULL';
    $obs = $conexion->real_escape_string($datos['obs'] ?? '');

    $sql = "UPDATE carreras SET nombre = '$nombre', entidad_id = $entidad_id, obs = '$obs' WHERE id = $id";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Eliminar carrera (eliminación lógica)
 */
function eliminar_carrera($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;
    $id = intval($id);
    $sql = "UPDATE carreras SET activo = 'N', eliminado = 'S' WHERE id = $id";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Obtener una carrera específica por ID
 */
function obtener_carrera_por_id($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return null;
    $id = intval($id);
    $sql = "SELECT c.id, c.nombre, c.entidad_id, e.nombre AS entidad_nombre, c.obs
            FROM carreras c
            LEFT JOIN entidades e ON c.entidad_id = e.id
            WHERE c.id = $id AND c.activo = 'S' AND c.eliminado = 'N'
            LIMIT 1";
    $result = $conexion->query($sql);
    $carrera = null;
    if ($result && $result->num_rows > 0) {
        $carrera = $result->fetch_assoc();
    }
    $conexion->close();
    return $carrera;
}