<?php
require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Obtener todos los tipos de entidades activos y no eliminados
 */
function obtener_tipos_entidades_en_sistema() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT id, nombre, activo, eliminado, obs FROM tipos_entidades WHERE activo = 'S' AND eliminado = 'N' ORDER BY nombre ASC";
    $result = $conexion->query($sql);
    $tipos = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $tipos[] = $row;
        }
    }
    $conexion->close();
    return $tipos;
}

/**
 * Crear nuevo tipo de entidad
 */
function crear_tipo_entidad($datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $nombre = $conexion->real_escape_string($datos['nombre'] ?? '');
    $obs = $conexion->real_escape_string($datos['obs'] ?? '');

    $sql = "INSERT INTO tipos_entidades (nombre, activo, eliminado, obs) VALUES ('$nombre', 'S', 'N', '$obs')";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Actualizar tipo de entidad existente
 */
function actualizar_tipo_entidad($id, $datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);
    $nombre = $conexion->real_escape_string($datos['nombre'] ?? '');
    $obs = $conexion->real_escape_string($datos['obs'] ?? '');

    $sql = "UPDATE tipos_entidades SET nombre = '$nombre', obs = '$obs' WHERE id = $id";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Eliminar tipo de entidad (eliminación lógica)
 */
function eliminar_tipo_entidad($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;
    $id = intval($id);
    $sql = "UPDATE tipos_entidades SET activo = 'N', eliminado = 'S' WHERE id = $id";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Obtener un tipo de entidad específico por ID
 */
function obtener_tipo_entidad_por_id($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return null;
    $id = intval($id);
    $sql = "SELECT id, nombre, activo, eliminado, obs FROM tipos_entidades WHERE id = $id AND activo = 'S' AND eliminado = 'N' LIMIT 1";
    $result = $conexion->query($sql);
    $tipo = null;
    if ($result && $result->num_rows > 0) {
        $tipo = $result->fetch_assoc();
    }
    $conexion->close();
    return $tipo;
}