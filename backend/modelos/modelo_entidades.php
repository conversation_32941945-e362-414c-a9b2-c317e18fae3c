<?php
require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Obtener todas las entidades activas y no eliminadas, con nombre del tipo de entidad
 */
function obtener_entidades_en_sistema() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT e.id, e.nombre, e.tipo_entidad_id, te.nombre AS tipo_entidad_nombre, e.obs
            FROM entidades e
            LEFT JOIN tipos_entidades te ON e.tipo_entidad_id = te.id
            WHERE e.activo = 'S' AND e.eliminado = 'N'
            ORDER BY e.nombre ASC";
    $result = $conexion->query($sql);
    $entidades = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $entidades[] = $row;
        }
    }
    $conexion->close();
    return $entidades;
}

/**
 * Obtener tipos de entidades activos para el select
 */
function obtener_tipos_entidades_para_select() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];
    $sql = "SELECT id, nombre FROM tipos_entidades WHERE activo = 'S' AND eliminado = 'N' ORDER BY nombre ASC";
    $result = $conexion->query($sql);
    $tipos = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $tipos[] = $row;
        }
    }
    $conexion->close();
    return $tipos;
}

/**
 * Crear nueva entidad
 */
function crear_entidad($datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $nombre = $conexion->real_escape_string($datos['nombre'] ?? '');
    $tipo_entidad_id = isset($datos['tipo_entidad_id']) && is_numeric($datos['tipo_entidad_id']) ? intval($datos['tipo_entidad_id']) : 'NULL';
    $obs = $conexion->real_escape_string($datos['obs'] ?? '');

    $sql = "INSERT INTO entidades (nombre, tipo_entidad_id, activo, eliminado, obs) VALUES ('$nombre', $tipo_entidad_id, 'S', 'N', '$obs')";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Actualizar entidad existente
 */
function actualizar_entidad($id, $datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);
    $nombre = $conexion->real_escape_string($datos['nombre'] ?? '');
    $tipo_entidad_id = isset($datos['tipo_entidad_id']) && is_numeric($datos['tipo_entidad_id']) ? intval($datos['tipo_entidad_id']) : 'NULL';
    $obs = $conexion->real_escape_string($datos['obs'] ?? '');

    $sql = "UPDATE entidades SET nombre = '$nombre', tipo_entidad_id = $tipo_entidad_id, obs = '$obs' WHERE id = $id";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Eliminar entidad (eliminación lógica)
 */
function eliminar_entidad($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;
    $id = intval($id);
    $sql = "UPDATE entidades SET activo = 'N', eliminado = 'S' WHERE id = $id";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Obtener una entidad específica por ID
 */
function obtener_entidad_por_id($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return null;
    $id = intval($id);
    $sql = "SELECT e.id, e.nombre, e.tipo_entidad_id, te.nombre AS tipo_entidad_nombre, e.obs
            FROM entidades e
            LEFT JOIN tipos_entidades te ON e.tipo_entidad_id = te.id
            WHERE e.id = $id AND e.activo = 'S' AND e.eliminado = 'N'
            LIMIT 1";
    $result = $conexion->query($sql);
    $entidad = null;
    if ($result && $result->num_rows > 0) {
        $entidad = $result->fetch_assoc();
    }
    $conexion->close();
    return $entidad;
}