<?php

require_once dirname(__DIR__, 2) . '/config/config.php';
require_once 'modelo_auditoria.php';

/**
 * Obtener todos los eventos con información del tipo de evento
 */
function obtener_eventos_en_sistema() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT
                e.id,
                e.evento,
                e.descripcion,
                e.tipo_evento,
                e.fecha_desde,
                e.fecha_desde_hora_inicio,
                e.fecha_desde_hora_final,
                e.fecha_hasta,
                e.fecha_hasta_hora_inicio,
                e.fecha_hasta_hora_final,
                e.obs,
                te.nombre as tipo_evento_nombre
            FROM eventos e
            LEFT JOIN tipos_eventos te ON e.tipo_evento = te.id
            WHERE e.activo = 'S' AND e.eliminado = 'N'
            ORDER BY e.fecha_desde DESC, e.fecha_desde_hora_inicio DESC";

    $result = $conexion->query($sql);
    $eventos = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $eventos[] = $row;
        }
    }

    $conexion->close();
    return $eventos;
}

/**
 * Obtener todos los tipos de eventos activos para el select
 */
function obtener_tipos_eventos_para_select() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT id, nombre 
            FROM tipos_eventos 
            WHERE activo = 'S' AND eliminado = 'N' 
            ORDER BY nombre ASC";
    
    $result = $conexion->query($sql);
    $tipos_eventos = [];
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $tipos_eventos[] = $row;
        }
    }
    
    $conexion->close();
    return $tipos_eventos;
}

/**
 * Crear nuevo evento
 */
function crear_evento($datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $evento = $conexion->real_escape_string($datos['evento'] ?? '');
    $descripcion = $conexion->real_escape_string($datos['descripcion'] ?? '');
    $tipo_evento = isset($datos['tipo_evento']) && is_numeric($datos['tipo_evento']) ? intval($datos['tipo_evento']) : 'NULL';
    $fecha_desde = isset($datos['fecha_desde']) && !empty($datos['fecha_desde']) ? "'" . $conexion->real_escape_string($datos['fecha_desde']) . "'" : 'NULL';
    $fecha_desde_hora_inicio = isset($datos['fecha_desde_hora_inicio']) && !empty($datos['fecha_desde_hora_inicio']) ? "'" . $conexion->real_escape_string($datos['fecha_desde_hora_inicio']) . "'" : 'NULL';
    $fecha_desde_hora_final = isset($datos['fecha_desde_hora_final']) && !empty($datos['fecha_desde_hora_final']) ? "'" . $conexion->real_escape_string($datos['fecha_desde_hora_final']) . "'" : 'NULL';
    $fecha_hasta = isset($datos['fecha_hasta']) && !empty($datos['fecha_hasta']) ? "'" . $conexion->real_escape_string($datos['fecha_hasta']) . "'" : 'NULL';
    $fecha_hasta_hora_inicio = isset($datos['fecha_hasta_hora_inicio']) && !empty($datos['fecha_hasta_hora_inicio']) ? "'" . $conexion->real_escape_string($datos['fecha_hasta_hora_inicio']) . "'" : 'NULL';
    $fecha_hasta_hora_final = isset($datos['fecha_hasta_hora_final']) && !empty($datos['fecha_hasta_hora_final']) ? "'" . $conexion->real_escape_string($datos['fecha_hasta_hora_final']) . "'" : 'NULL';
    $obs = $conexion->real_escape_string($datos['obs'] ?? '');

    $sql = "INSERT INTO eventos (evento, descripcion, tipo_evento, fecha_desde, fecha_desde_hora_inicio, fecha_desde_hora_final, fecha_hasta, fecha_hasta_hora_inicio, fecha_hasta_hora_final, obs, activo, eliminado)
            VALUES ('$evento', '$descripcion', $tipo_evento, $fecha_desde, $fecha_desde_hora_inicio, $fecha_desde_hora_final, $fecha_hasta, $fecha_hasta_hora_inicio, $fecha_hasta_hora_final, '$obs', 'S', 'N')";

    $ok = $conexion->query($sql);

    // Auditoría: Registrar creación
    if ($ok) {
        $nuevo_id = $conexion->insert_id;
        auditoria_create('eventos', $nuevo_id, $datos, 'Evento creado');
    }

    $conexion->close();
    return $ok;
}

/**
 * Actualizar evento existente
 */
function actualizar_evento($id, $datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);
    $evento = $conexion->real_escape_string($datos['evento'] ?? '');
    $descripcion = $conexion->real_escape_string($datos['descripcion'] ?? '');
    $tipo_evento = isset($datos['tipo_evento']) && is_numeric($datos['tipo_evento']) ? intval($datos['tipo_evento']) : 'NULL';
    $fecha_desde = isset($datos['fecha_desde']) && !empty($datos['fecha_desde']) ? "'" . $conexion->real_escape_string($datos['fecha_desde']) . "'" : 'NULL';
    $fecha_desde_hora_inicio = isset($datos['fecha_desde_hora_inicio']) && !empty($datos['fecha_desde_hora_inicio']) ? "'" . $conexion->real_escape_string($datos['fecha_desde_hora_inicio']) . "'" : 'NULL';
    $fecha_desde_hora_final = isset($datos['fecha_desde_hora_final']) && !empty($datos['fecha_desde_hora_final']) ? "'" . $conexion->real_escape_string($datos['fecha_desde_hora_final']) . "'" : 'NULL';
    $fecha_hasta = isset($datos['fecha_hasta']) && !empty($datos['fecha_hasta']) ? "'" . $conexion->real_escape_string($datos['fecha_hasta']) . "'" : 'NULL';
    $fecha_hasta_hora_inicio = isset($datos['fecha_hasta_hora_inicio']) && !empty($datos['fecha_hasta_hora_inicio']) ? "'" . $conexion->real_escape_string($datos['fecha_hasta_hora_inicio']) . "'" : 'NULL';
    $fecha_hasta_hora_final = isset($datos['fecha_hasta_hora_final']) && !empty($datos['fecha_hasta_hora_final']) ? "'" . $conexion->real_escape_string($datos['fecha_hasta_hora_final']) . "'" : 'NULL';
    $obs = $conexion->real_escape_string($datos['obs'] ?? '');

    // Obtener datos anteriores para auditoría
    $datos_anteriores = obtener_datos_anteriores('eventos', $id);

    $sql = "UPDATE eventos
            SET evento = '$evento',
                descripcion = '$descripcion',
                tipo_evento = $tipo_evento,
                fecha_desde = $fecha_desde,
                fecha_desde_hora_inicio = $fecha_desde_hora_inicio,
                fecha_desde_hora_final = $fecha_desde_hora_final,
                fecha_hasta = $fecha_hasta,
                fecha_hasta_hora_inicio = $fecha_hasta_hora_inicio,
                fecha_hasta_hora_final = $fecha_hasta_hora_final,
                obs = '$obs'
            WHERE id = $id";

    $ok = $conexion->query($sql);

    // Auditoría: Registrar actualización
    if ($ok && $datos_anteriores) {
        auditoria_update('eventos', $id, $datos_anteriores, $datos, 'Evento actualizado');
    }

    $conexion->close();
    return $ok;
}

/**
 * Eliminar evento (eliminación lógica)
 */
function eliminar_evento($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);

    // Obtener datos anteriores para auditoría
    $datos_anteriores = obtener_datos_anteriores('eventos', $id);

    $sql = "UPDATE eventos SET activo = 'N', eliminado = 'S' WHERE id = $id";

    $ok = $conexion->query($sql);

    // Auditoría: Registrar eliminación
    if ($ok && $datos_anteriores) {
        auditoria_delete('eventos', $id, $datos_anteriores, 'Evento eliminado (lógicamente)');
    }

    $conexion->close();
    return $ok;
}

/**
 * Obtener un evento específico por ID
 */
function obtener_evento_por_id($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return null;

    $id = intval($id);
    $sql = "SELECT
                e.id,
                e.evento,
                e.descripcion,
                e.tipo_evento,
                e.fecha_desde,
                e.fecha_desde_hora_inicio,
                e.fecha_desde_hora_final,
                e.fecha_hasta,
                e.fecha_hasta_hora_inicio,
                e.fecha_hasta_hora_final,
                e.obs,
                te.nombre as tipo_evento_nombre
            FROM eventos e
            LEFT JOIN tipos_eventos te ON e.tipo_evento = te.id
            WHERE e.id = $id AND e.activo = 'S' AND e.eliminado = 'N'
            LIMIT 1";

    $result = $conexion->query($sql);
    $evento = null;

    if ($result && $result->num_rows > 0) {
        $evento = $result->fetch_assoc();
    }

    $conexion->close();
    return $evento;
}
