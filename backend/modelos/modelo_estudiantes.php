<?php
require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

function obtener_todos_estudiantes() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $estudiantes = [];
    $sql = "SELECT e.*, u.nombres AS usuario_nombres, u.apellidos AS usuario_apellidos, c.nombre AS carrera_nombre, ent.nombre AS entidad_nombre
            FROM estudiantes e
            LEFT JOIN usuarios u ON e.usuario_id = u.id
            LEFT JOIN carreras c ON e.carrera_id = c.id
            LEFT JOIN entidades ent ON c.entidad_id = ent.id
            WHERE e.eliminado = 'N' AND e.activo = 'S'";
    $result = $conexion->query($sql);
    while ($row = $result->fetch_assoc()) {
        $estudiantes[] = $row;
    }
    $conexion->close();
    return $estudiantes;
}

function existe_estudiante($usuario_id, $carrera_id, $id_excluir = null) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $sql = "SELECT id FROM estudiantes WHERE usuario_id = ? AND carrera_id = ? AND eliminado = 'N' AND activo = 'S'";
    if ($id_excluir) {
        $sql .= " AND id != ?";
    }
    $stmt = $conexion->prepare($sql);
    if ($id_excluir) {
        $stmt->bind_param("iii", $usuario_id, $carrera_id, $id_excluir);
    } else {
        $stmt->bind_param("ii", $usuario_id, $carrera_id);
    }
    $stmt->execute();
    $stmt->store_result();
    $existe = $stmt->num_rows > 0;
    $stmt->close();
    $conexion->close();
    return $existe;
}

function crear_estudiante($datos) {
    if (existe_estudiante($datos['usuario_id'], $datos['carrera_id'])) {
        return 'duplicado';
    }
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $stmt = $conexion->prepare("INSERT INTO estudiantes (usuario_id, carrera_id, activo, eliminado, obs) VALUES (?, ?, 'S', 'N', ?)");
    $stmt->bind_param("iis", $datos['usuario_id'], $datos['carrera_id'], $datos['obs']);
    $ok = $stmt->execute();
    $stmt->close();
    $conexion->close();
    return $ok;
}

function actualizar_estudiante($id, $datos) {
    if (existe_estudiante($datos['usuario_id'], $datos['carrera_id'], $id)) {
        return 'duplicado';
    }
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $stmt = $conexion->prepare("UPDATE estudiantes SET usuario_id=?, carrera_id=?, obs=? WHERE id=? AND eliminado='N' AND activo='S'");
    $stmt->bind_param("iisi", $datos['usuario_id'], $datos['carrera_id'], $datos['obs'], $id);
    $ok = $stmt->execute();
    $stmt->close();
    $conexion->close();
    return $ok;
}

function eliminar_estudiante($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $stmt = $conexion->prepare("UPDATE estudiantes SET eliminado='S', activo='N' WHERE id=?");
    $stmt->bind_param("i", $id);
    $ok = $stmt->execute();
    $stmt->close();
    $conexion->close();
    return $ok;
}

function obtener_usuarios_activos() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $usuarios = [];
    $sql = "SELECT id, nombres, apellidos FROM usuarios WHERE eliminado='N' AND activo='S'";
    $result = $conexion->query($sql);
    while ($row = $result->fetch_assoc()) {
        $usuarios[] = $row;
    }
    $conexion->close();
    return $usuarios;
}

function obtener_carreras_activas() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $carreras = [];
    $sql = "SELECT c.id, c.nombre, e.nombre AS entidad_nombre FROM carreras c LEFT JOIN entidades e ON c.entidad_id = e.id WHERE c.eliminado='N' AND c.activo='S'";
    $result = $conexion->query($sql);
    while ($row = $result->fetch_assoc()) {
        $carreras[] = $row;
    }
    $conexion->close();
    return $carreras;
}
