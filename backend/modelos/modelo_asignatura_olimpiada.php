<?php
require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

function obtener_todas_asignatura_olimpiada() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $resultados = [];
    $sql = "SELECT ao.*, a.nombre AS asignatura_nombre, o.nombre AS olimpiada_nombre
            FROM asignatura_olimpiada ao
            LEFT JOIN asignaturas a ON ao.asignatura_id = a.id
            LEFT JOIN olimpiadas o ON ao.olimpiada_id = o.id
            WHERE ao.eliminado = 'N' AND ao.activo = 'S'";
    $result = $conexion->query($sql);
    while ($row = $result->fetch_assoc()) {
        $resultados[] = $row;
    }
    $conexion->close();
    return $resultados;
}

function existe_asignatura_olimpiada($asignatura_id, $olimpiada_id, $id_excluir = null) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $sql = "SELECT id FROM asignatura_olimpiada WHERE asignatura_id = ? AND olimpiada_id = ? AND eliminado = 'N' AND activo = 'S'";
    if ($id_excluir) {
        $sql .= " AND id != ?";
    }
    $stmt = $conexion->prepare($sql);
    if ($id_excluir) {
        $stmt->bind_param("iii", $asignatura_id, $olimpiada_id, $id_excluir);
    } else {
        $stmt->bind_param("ii", $asignatura_id, $olimpiada_id);
    }
    $stmt->execute();
    $stmt->store_result();
    $existe = $stmt->num_rows > 0;
    $stmt->close();
    $conexion->close();
    return $existe;
}

function crear_asignatura_olimpiada($datos) {
    if (existe_asignatura_olimpiada($datos['asignatura_id'], $datos['olimpiada_id'])) {
        return 'duplicado';
    }
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $stmt = $conexion->prepare("INSERT INTO asignatura_olimpiada (asignatura_id, olimpiada_id, activo, eliminado, obs) VALUES (?, ?, 'S', 'N', ?)");
    $stmt->bind_param("iis", $datos['asignatura_id'], $datos['olimpiada_id'], $datos['obs']);
    $ok = $stmt->execute();
    $stmt->close();
    $conexion->close();
    return $ok;
}

function actualizar_asignatura_olimpiada($id, $datos) {
    if (existe_asignatura_olimpiada($datos['asignatura_id'], $datos['olimpiada_id'], $id)) {
        return 'duplicado';
    }
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $stmt = $conexion->prepare("UPDATE asignatura_olimpiada SET asignatura_id=?, olimpiada_id=?, obs=? WHERE id=? AND eliminado='N' AND activo='S'");
    $stmt->bind_param("iisi", $datos['asignatura_id'], $datos['olimpiada_id'], $datos['obs'], $id);
    $ok = $stmt->execute();
    $stmt->close();
    $conexion->close();
    return $ok;
}

function eliminar_asignatura_olimpiada($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $stmt = $conexion->prepare("UPDATE asignatura_olimpiada SET eliminado='S', activo='N' WHERE id=?");
    $stmt->bind_param("i", $id);
    $ok = $stmt->execute();
    $stmt->close();
    $conexion->close();
    return $ok;
}

function obtener_asignaturas() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $asignaturas = [];
    $sql = "SELECT id, nombre FROM asignaturas WHERE eliminado='N' AND activo='S'";
    $result = $conexion->query($sql);
    while ($row = $result->fetch_assoc()) {
        $asignaturas[] = $row;
    }
    $conexion->close();
    return $asignaturas;
}

function obtener_olimpiadas() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $olimpiadas = [];
    $sql = "SELECT id, nombre FROM olimpiadas WHERE eliminado='N' AND activo='S'";
    $result = $conexion->query($sql);
    while ($row = $result->fetch_assoc()) {
        $olimpiadas[] = $row;
    }
    $conexion->close();
    return $olimpiadas;
}
