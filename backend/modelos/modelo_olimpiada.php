<?php
require_once dirname(__DIR__, 2) . '/config/config.php';
require_once 'modelo_auditoria.php';

// Iniciar sesión si no está iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Obtener todas las olimpiadas activas y no eliminadas
 */
function obtener_olimpiadas_en_sistema() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT id, nombre, Interna, fecha_desde, fecha_hasta, detalles, activo, eliminado, obs FROM olimpiadas WHERE activo = 'S' AND eliminado = 'N' ORDER BY fecha_desde DESC, id DESC";
    $result = $conexion->query($sql);
    $olimpiadas = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $olimpiadas[] = $row;
        }
    }
    $conexion->close();
    return $olimpiadas;
}

/**
 * Crear nueva olimpiada
 */
function crear_olimpiada($datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $nombre = $conexion->real_escape_string($datos['nombre'] ?? '');
    $Interna = $conexion->real_escape_string($datos['Interna'] ?? 'S');
    $fecha_desde = isset($datos['fecha_desde']) && !empty($datos['fecha_desde']) ? "'" . $conexion->real_escape_string($datos['fecha_desde']) . "'" : 'NULL';
    $fecha_hasta = isset($datos['fecha_hasta']) && !empty($datos['fecha_hasta']) ? "'" . $conexion->real_escape_string($datos['fecha_hasta']) . "'" : 'NULL';
    $detalles = $conexion->real_escape_string($datos['detalles'] ?? '');
    $obs = $conexion->real_escape_string($datos['obs'] ?? '');

    $sql = "INSERT INTO olimpiadas (nombre, Interna, fecha_desde, fecha_hasta, detalles, activo, eliminado, obs) VALUES ('$nombre', '$Interna', $fecha_desde, $fecha_hasta, '$detalles', 'S', 'N', '$obs')";
    $ok = $conexion->query($sql);

    // Auditoría: Registrar creación
    if ($ok) {
        $nuevo_id = $conexion->insert_id;
        $datos_nuevos = [
            'nombre' => $datos['nombre'],
            'Interna' => $datos['Interna'],
            'fecha_desde' => $datos['fecha_desde'],
            'fecha_hasta' => $datos['fecha_hasta'],
            'detalles' => $datos['detalles'],
            'obs' => $datos['obs'],
            'activo' => 'S',
            'eliminado' => 'N'
        ];
        auditoria_create('olimpiadas', $nuevo_id, $datos_nuevos, 'Olimpiada creada');
    }

    $conexion->close();
    return $ok;
}

/**
 * Actualizar olimpiada existente
 */
function actualizar_olimpiada($id, $datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);
    $nombre = $conexion->real_escape_string($datos['nombre'] ?? '');
    $Interna = $conexion->real_escape_string($datos['Interna'] ?? 'S');
    $fecha_desde = isset($datos['fecha_desde']) && !empty($datos['fecha_desde']) ? "'" . $conexion->real_escape_string($datos['fecha_desde']) . "'" : 'NULL';
    $fecha_hasta = isset($datos['fecha_hasta']) && !empty($datos['fecha_hasta']) ? "'" . $conexion->real_escape_string($datos['fecha_hasta']) . "'" : 'NULL';
    $detalles = $conexion->real_escape_string($datos['detalles'] ?? '');
    $obs = $conexion->real_escape_string($datos['obs'] ?? '');

    $sql = "UPDATE olimpiadas SET nombre = '$nombre', Interna = '$Interna', fecha_desde = $fecha_desde, fecha_hasta = $fecha_hasta, detalles = '$detalles', obs = '$obs' WHERE id = $id";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Eliminar olimpiada (eliminación lógica)
 */
function eliminar_olimpiada($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;
    $id = intval($id);
    $sql = "UPDATE olimpiadas SET activo = 'N', eliminado = 'S' WHERE id = $id";
    $ok = $conexion->query($sql);
    $conexion->close();
    return $ok;
}

/**
 * Obtener una olimpiada específica por ID
 */
function obtener_olimpiada_por_id($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return null;
    $id = intval($id);
    $sql = "SELECT id, nombre, Interna, fecha_desde, fecha_hasta, detalles, activo, eliminado, obs FROM olimpiadas WHERE id = $id AND activo = 'S' AND eliminado = 'N' LIMIT 1";
    $result = $conexion->query($sql);
    $olimpiada = null;
    if ($result && $result->num_rows > 0) {
        $olimpiada = $result->fetch_assoc();
    }
    $conexion->close();
    return $olimpiada;
}
