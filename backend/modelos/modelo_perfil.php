<?php
function obtener_usuario_por_id($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;
    $stmt = $conexion->prepare("SELECT * FROM usuarios WHERE id = ? AND activo = 'S' AND eliminado = 'N' LIMIT 1");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $resultado = $stmt->get_result();
    $usuario = $resultado->fetch_assoc();
    $stmt->close();
    $conexion->close();
    return $usuario;
}

function actualizar_usuario($id, $datos) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;
    $stmt = $conexion->prepare("UPDATE usuarios SET apellidos=?, nombres=?, email_institucional=?, email_alterno=?, sexo=?, estado_civil=?, telefono=?, celular=? WHERE id=? AND activo='S' AND eliminado='N'");
    $stmt->bind_param(
        "ssssssssi",
        $datos['apellidos'],
        $datos['nombres'],
        $datos['email_institucional'],
        $datos['email_alterno'],
        $datos['sexo'],
        $datos['estado_civil'],
        $datos['telefono'],
        $datos['celular'],
        $id
    );
    $ok = $stmt->execute();
    $stmt->close();
    $conexion->close();
    return $ok;
}
?>