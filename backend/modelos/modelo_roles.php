<?php

require_once dirname(__DIR__, 2) . '/config/config.php';
require_once 'modelo_auditoria.php';

function obtener_roles_en_sistema() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT id, nombre, obs FROM roles WHERE activo = 'S' AND eliminado = 'N' ORDER BY id ASC";
    $result = $conexion->query($sql);

    $roles = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $roles[] = $row;
        }
    }
    $conexion->close();
    return $roles;
}

function rol_existe($nombre, $id_excluir = null) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $nombre = $conexion->real_escape_string($nombre);
    $sql = "SELECT id FROM roles WHERE nombre = '$nombre' AND activo = 'S' AND eliminado = 'N'";
    if ($id_excluir !== null) {
        $id_excluir = intval($id_excluir);
        $sql .= " AND id != $id_excluir";
    }
    $result = $conexion->query($sql);
    $existe = ($result && $result->num_rows > 0);
    $conexion->close();
    return $existe;
}

function crear_rol($nombre, $obs) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $nombre = $conexion->real_escape_string($nombre);
    $obs = $conexion->real_escape_string($obs ?? '');
    $sql = "INSERT INTO roles (nombre, obs, activo, eliminado) VALUES ('$nombre', '$obs', 'S', 'N')";
    $ok = $conexion->query($sql);

    // Auditoría: Registrar creación
    if ($ok) {
        $nuevo_id = $conexion->insert_id;
        $datos_nuevos = ['nombre' => $nombre, 'obs' => $obs, 'activo' => 'S', 'eliminado' => 'N'];
        auditoria_create('roles', $nuevo_id, $datos_nuevos, 'Rol creado');
    }

    $conexion->close();
    return $ok;
}

function actualizar_rol($id, $nombre, $obs) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);

    // Obtener datos anteriores para auditoría
    $datos_anteriores = obtener_datos_anteriores('roles', $id);

    $nombre = $conexion->real_escape_string($nombre);
    $obs = $conexion->real_escape_string($obs ?? '');
    $sql = "UPDATE roles SET nombre = '$nombre', obs = '$obs' WHERE id = $id";
    $ok = $conexion->query($sql);

    // Auditoría: Registrar actualización
    if ($ok && $datos_anteriores) {
        $datos_nuevos = ['nombre' => $nombre, 'obs' => $obs];
        auditoria_update('roles', $id, $datos_anteriores, $datos_nuevos, 'Rol actualizado');
    }

    $conexion->close();
    return $ok;
}

function eliminar_rol($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $id = intval($id);

    // Obtener datos anteriores para auditoría
    $datos_anteriores = obtener_datos_anteriores('roles', $id);

    $sql = "UPDATE roles SET activo = 'N', eliminado = 'S' WHERE id = $id";
    $ok = $conexion->query($sql);

    // Auditoría: Registrar eliminación
    if ($ok && $datos_anteriores) {
        auditoria_delete('roles', $id, $datos_anteriores, 'Rol eliminado (lógicamente)');
    }

    $conexion->close();
    return $ok;
}