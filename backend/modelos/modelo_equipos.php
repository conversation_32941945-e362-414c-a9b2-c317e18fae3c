<?php

require_once dirname(__DIR__, 2) . '/config/config.php';
require_once 'modelo_auditoria.php';

// Iniciar sesión si no está iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Obtener todos los equipos activos
 */
function obtener_todos_equipos() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT id, nombre, obs FROM equipos WHERE eliminado = 'N' AND activo = 'S' ORDER BY nombre ASC";
    $result = $conexion->query($sql);

    $equipos = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $equipos[] = $row;
        }
    }

    $conexion->close();
    return $equipos;
}

/**
 * Obtener un equipo por ID
 */
function obtener_equipo_por_id($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $stmt = $conexion->prepare("SELECT id, nombre, obs FROM equipos WHERE id = ? AND eliminado = 'N' AND activo = 'S' LIMIT 1");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $resultado = $stmt->get_result();
    $equipo = $resultado->fetch_assoc();

    $stmt->close();
    $conexion->close();
    return $equipo;
}

/**
 * Verificar si existe un equipo con el mismo nombre
 */
function existe_equipo($nombre, $id_excluir = null) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $sql = "SELECT id FROM equipos WHERE nombre = ? AND eliminado = 'N' AND activo = 'S'";
    if ($id_excluir) {
        $sql .= " AND id != ?";
    }

    $stmt = $conexion->prepare($sql);
    if ($id_excluir) {
        $stmt->bind_param("si", $nombre, $id_excluir);
    } else {
        $stmt->bind_param("s", $nombre);
    }

    $stmt->execute();
    $stmt->store_result();
    $existe = $stmt->num_rows > 0;

    $stmt->close();
    $conexion->close();
    return $existe;
}

/**
 * Crear nuevo equipo
 */
function crear_equipo($datos) {
    // Verificar duplicados
    if (existe_equipo($datos['nombre'])) {
        return 'duplicado';
    }

    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    $stmt = $conexion->prepare("INSERT INTO equipos (nombre, obs, activo, eliminado) VALUES (?, ?, 'S', 'N')");
    $stmt->bind_param("ss", $datos['nombre'], $datos['obs']);

    try {
        $ok = $stmt->execute();

        // Auditoría: Registrar creación
        if ($ok) {
            $nuevo_id = $conexion->insert_id;
            $datos_nuevos = [
                'nombre' => $datos['nombre'],
                'obs' => $datos['obs'],
                'activo' => 'S',
                'eliminado' => 'N'
            ];
            auditoria_create('equipos', $nuevo_id, $datos_nuevos, 'Equipo creado');
        }
    } catch (mysqli_sql_exception $e) {
        $ok = false;
    }

    $stmt->close();
    $conexion->close();
    return $ok;
}

/**
 * Actualizar equipo existente
 */
function actualizar_equipo($id, $datos) {
    // Verificar duplicados
    if (existe_equipo($datos['nombre'], $id)) {
        return 'duplicado';
    }

    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    // Obtener datos anteriores para auditoría
    $datos_anteriores = obtener_datos_anteriores('equipos', $id);

    $stmt = $conexion->prepare("UPDATE equipos SET nombre=?, obs=? WHERE id=? AND eliminado='N' AND activo='S'");
    $stmt->bind_param("ssi", $datos['nombre'], $datos['obs'], $id);

    try {
        $ok = $stmt->execute();

        // Auditoría: Registrar actualización
        if ($ok && $datos_anteriores) {
            auditoria_update('equipos', $id, $datos_anteriores, $datos, 'Equipo actualizado');
        }
    } catch (mysqli_sql_exception $e) {
        $ok = false;
    }

    $stmt->close();
    $conexion->close();
    return $ok;
}

/**
 * Eliminar equipo (eliminación lógica)
 */
function eliminar_equipo($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return false;

    // Obtener datos anteriores para auditoría
    $datos_anteriores = obtener_datos_anteriores('equipos', $id);

    $stmt = $conexion->prepare("UPDATE equipos SET eliminado='S', activo='N' WHERE id=?");
    $stmt->bind_param("i", $id);

    try {
        $ok = $stmt->execute();

        // Auditoría: Registrar eliminación
        if ($ok && $datos_anteriores) {
            auditoria_delete('equipos', $id, $datos_anteriores, 'Equipo eliminado (lógicamente)');
        }
    } catch (mysqli_sql_exception $e) {
        $ok = false;
    }

    $stmt->close();
    $conexion->close();
    return $ok;
}

?>