<?php
require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

function obtener_todos_equipos() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $equipos = [];
    $sql = "SELECT * FROM equipos WHERE eliminado = 'N' AND activo = 'S'";
    $result = $conexion->query($sql);
    while ($row = $result->fetch_assoc()) {
        $equipos[] = $row;
    }
    $conexion->close();
    return $equipos;
}

function existe_equipo($nombre, $id_excluir = null) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $sql = "SELECT id FROM equipos WHERE nombre = ? AND eliminado = 'N' AND activo = 'S'";
    if ($id_excluir) {
        $sql .= " AND id != ?";
    }
    $stmt = $conexion->prepare($sql);
    if ($id_excluir) {
        $stmt->bind_param("si", $nombre, $id_excluir);
    } else {
        $stmt->bind_param("s", $nombre);
    }
    $stmt->execute();
    $stmt->store_result();
    $existe = $stmt->num_rows > 0;
    $stmt->close();
    $conexion->close();
    return $existe;
}

function crear_equipo($datos) {
    if (existe_equipo($datos['nombre'])) {
        return 'duplicado';
    }
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $stmt = $conexion->prepare("INSERT INTO equipos (nombre, activo, eliminado, obs) VALUES (?, 'S', 'N', ?)");
    $stmt->bind_param("ss", $datos['nombre'], $datos['obs']);
    $ok = $stmt->execute();
    $stmt->close();
    $conexion->close();
    return $ok;
}

function actualizar_equipo($id, $datos) {
    if (existe_equipo($datos['nombre'], $id)) {
        return 'duplicado';
    }
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $stmt = $conexion->prepare("UPDATE equipos SET nombre=?, obs=? WHERE id=? AND eliminado='N' AND activo='S'");
    $stmt->bind_param("ssi", $datos['nombre'], $datos['obs'], $id);
    $ok = $stmt->execute();
    $stmt->close();
    $conexion->close();
    return $ok;
}

function eliminar_equipo($id) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $stmt = $conexion->prepare("UPDATE equipos SET eliminado='S', activo='N' WHERE id=?");
    $stmt->bind_param("i", $id);
    $ok = $stmt->execute();
    $stmt->close();
    $conexion->close();
    return $ok;
}
?>