<?php
require_once dirname(__DIR__, 2) . '/config/config.php';
require_once BASE_PATH . 'publico/PHP/funciones.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    ir_a_pagina(DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_asignatura_olimpiada.php';

$mensaje = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';
    $asignatura_id = isset($_POST['asignatura_id']) && $_POST['asignatura_id'] !== '' ? intval($_POST['asignatura_id']) : null;
    $olimpiada_id = isset($_POST['olimpiada_id']) && $_POST['olimpiada_id'] !== '' ? intval($_POST['olimpiada_id']) : null;
    $obs = isset($_POST['obs']) ? trim($_POST['obs']) : null;

    if ($accion === 'crear') {
        $datos = [
            'asignatura_id' => $asignatura_id,
            'olimpiada_id' => $olimpiada_id,
            'obs' => $obs
        ];
        $ok = crear_asignatura_olimpiada($datos);
        if ($ok === 'duplicado') {
            $mensaje = "Ya existe un registro con la misma asignatura y olimpiada.";
        } else {
            $mensaje = $ok ? "Registro creado correctamente." : "Error al crear el registro.";
        }
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $datos = [
            'asignatura_id' => $asignatura_id,
            'olimpiada_id' => $olimpiada_id,
            'obs' => $obs
        ];
        $ok = actualizar_asignatura_olimpiada($id, $datos);
        if ($ok === 'duplicado') {
            $mensaje = "Ya existe un registro con la misma asignatura y olimpiada.";
        } else {
            $mensaje = $ok ? "Registro actualizado correctamente." : "Error al actualizar el registro.";
        }
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $ok = eliminar_asignatura_olimpiada($id);
        $mensaje = $ok ? "Registro eliminado correctamente." : "Error al eliminar el registro.";
    }
}

$asignatura_olimpiada = obtener_todas_asignatura_olimpiada();
$asignaturas = obtener_asignaturas();
$olimpiadas = obtener_olimpiadas();

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_asignatura_olimpiada.php';
require_once BASE_PATH . 'backend/vistas/footer.php';
?>
