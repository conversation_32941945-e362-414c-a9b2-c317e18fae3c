<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    header('Location: ' . DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_carreras.php';

$mensaje = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';

    if ($accion === 'crear') {
        $nombre = isset($_POST['nombre']) && trim($_POST['nombre']) !== '' ? trim($_POST['nombre']) : null;
        $entidad_id = isset($_POST['entidad_id']) && is_numeric($_POST['entidad_id']) ? intval($_POST['entidad_id']) : null;
        $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;

        if ($nombre && $entidad_id) {
            $datos = [
                'nombre' => $nombre,
                'entidad_id' => $entidad_id,
                'obs' => $obs
            ];
            $ok = crear_carrera($datos);
            $mensaje = $ok ? "Carrera creada correctamente." : "Error al crear la carrera.";
        } else {
            $mensaje = "El nombre y la entidad son requeridos.";
        }
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $nombre = isset($_POST['nombre']) && trim($_POST['nombre']) !== '' ? trim($_POST['nombre']) : null;
        $entidad_id = isset($_POST['entidad_id']) && is_numeric($_POST['entidad_id']) ? intval($_POST['entidad_id']) : null;
        $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;

        if ($nombre && $entidad_id) {
            $datos = [
                'nombre' => $nombre,
                'entidad_id' => $entidad_id,
                'obs' => $obs
            ];
            $ok = actualizar_carrera($id, $datos);
            $mensaje = $ok ? "Carrera actualizada correctamente." : "Error al actualizar la carrera.";
        } else {
            $mensaje = "El nombre y la entidad son requeridos.";
        }
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $ok = eliminar_carrera($id);
        $mensaje = $ok ? "Carrera eliminada correctamente." : "Error al eliminar la carrera.";
    }
}

// Obtener datos para la vista
$carreras_en_sistema = obtener_carreras_en_sistema();
$entidades_para_select = obtener_entidades_para_select();
if (!is_array($carreras_en_sistema)) $carreras_en_sistema = [];
if (!is_array($entidades_para_select)) $entidades_para_select = [];

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_carreras.php';
require_once BASE_PATH . 'backend/vistas/footer.php';