<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    header('Location: ' . DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_usuarios_roles.php';

$mensaje = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';
    
    if ($accion === 'crear') {
        $usuario_id = isset($_POST['usuario_id']) && is_numeric($_POST['usuario_id']) ? intval($_POST['usuario_id']) : null;
        $rol_id = isset($_POST['rol_id']) && is_numeric($_POST['rol_id']) ? intval($_POST['rol_id']) : null;
        $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;
        
        if ($usuario_id && $rol_id) {
            if (!usuario_rol_existe($usuario_id, $rol_id)) {
                $ok = crear_usuario_rol($usuario_id, $rol_id, $obs);
                $mensaje = $ok ? "Asignación creada correctamente." : "Error al crear la asignación.";
            } else {
                $mensaje = "Esta asignación usuario-rol ya existe.";
            }
        } else {
            $mensaje = "Debe seleccionar un usuario y un rol válidos.";
        }
        
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $usuario_id = isset($_POST['usuario_id']) && is_numeric($_POST['usuario_id']) ? intval($_POST['usuario_id']) : null;
        $rol_id = isset($_POST['rol_id']) && is_numeric($_POST['rol_id']) ? intval($_POST['rol_id']) : null;
        $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;
        
        if ($usuario_id && $rol_id) {
            if (!usuario_rol_existe($usuario_id, $rol_id, $id)) {
                $ok = actualizar_usuario_rol($id, $usuario_id, $rol_id, $obs);
                $mensaje = $ok ? "Asignación actualizada correctamente." : "Error al actualizar la asignación.";
            } else {
                $mensaje = "Esta asignación usuario-rol ya existe.";
            }
        } else {
            $mensaje = "Debe seleccionar un usuario y un rol válidos.";
        }
        
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $ok = eliminar_usuario_rol($id);
        $mensaje = $ok ? "Asignación eliminada correctamente." : "Error al eliminar la asignación.";
    }
}

// Obtener datos para la vista
$usuarios_roles = obtener_usuarios_roles();
$usuarios_para_select = obtener_usuarios_para_select();
$roles_para_select = obtener_roles_para_select();

if (!is_array($usuarios_roles)) $usuarios_roles = [];
if (!is_array($usuarios_para_select)) $usuarios_para_select = [];
if (!is_array($roles_para_select)) $roles_para_select = [];

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_usuarios_roles.php';
require_once BASE_PATH . 'backend/vistas/footer.php';
?>
