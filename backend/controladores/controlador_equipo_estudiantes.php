<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once dirname(__DIR__, 2) . '/config/config.php';
require_once BASE_PATH . 'publico/PHP/funciones.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    ir_a_pagina(DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_equipo_estudiantes.php';

$mensaje = '';

// Manejar solicitudes AJAX para obtener datos de asignación
if (isset($_GET['ajax']) && $_GET['ajax'] === 'obtener_asignacion' && isset($_GET['id'])) {
    header('Content-Type: application/json');
    $id = intval($_GET['id']);
    $asignacion = obtener_equipo_estudiante_por_id($id);
    echo json_encode($asignacion);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';

    if ($accion === 'crear') {
        $equipo_id = isset($_POST['equipo_id']) ? intval($_POST['equipo_id']) : 0;
        $estudiante_id = isset($_POST['estudiante_id']) ? intval($_POST['estudiante_id']) : 0;
        $obs = isset($_POST['obs']) ? trim($_POST['obs']) : '';

        if ($equipo_id <= 0) {
            $mensaje = "Debe seleccionar un equipo válido.";
        } elseif ($estudiante_id <= 0) {
            $mensaje = "Debe seleccionar un estudiante válido.";
        } else {
            $datos = [
                'equipo_id' => $equipo_id,
                'estudiante_id' => $estudiante_id,
                'obs' => $obs
            ];
            $resultado = crear_equipo_estudiante($datos);
            if ($resultado === 'duplicado') {
                $mensaje = "El estudiante ya está asignado a este equipo.";
            } elseif ($resultado) {
                $mensaje = "Asignación creada correctamente.";
            } else {
                $mensaje = "Error al crear la asignación.";
            }
        }
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $equipo_id = isset($_POST['equipo_id']) ? intval($_POST['equipo_id']) : 0;
        $estudiante_id = isset($_POST['estudiante_id']) ? intval($_POST['estudiante_id']) : 0;
        $obs = isset($_POST['obs']) ? trim($_POST['obs']) : '';

        if ($equipo_id <= 0) {
            $mensaje = "Debe seleccionar un equipo válido.";
        } elseif ($estudiante_id <= 0) {
            $mensaje = "Debe seleccionar un estudiante válido.";
        } else {
            $datos = [
                'equipo_id' => $equipo_id,
                'estudiante_id' => $estudiante_id,
                'obs' => $obs
            ];
            $resultado = actualizar_equipo_estudiante($id, $datos);
            if ($resultado === 'duplicado') {
                $mensaje = "El estudiante ya está asignado a este equipo.";
            } elseif ($resultado) {
                $mensaje = "Asignación actualizada correctamente.";
            } else {
                $mensaje = "Error al actualizar la asignación.";
            }
        }
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $resultado = eliminar_equipo_estudiante($id);
        if ($resultado) {
            $mensaje = "Asignación eliminada correctamente.";
        } else {
            $mensaje = "Error al eliminar la asignación.";
        }
    }
}

// Obtener datos para la vista
$asignaciones = obtener_equipo_estudiantes();
$equipos_para_select = obtener_equipos_para_select();
$estudiantes_para_select = obtener_estudiantes_para_select();

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_equipo_estudiantes.php';
require_once BASE_PATH . 'backend/vistas/footer.php';
?>