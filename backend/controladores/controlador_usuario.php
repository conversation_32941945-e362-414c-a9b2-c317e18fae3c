<?php
require_once dirname(__DIR__, 2) . '/config/config.php';
require_once BASE_PATH . 'publico/PHP/funciones.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    ir_a_pagina(DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_usuario.php';

$mensaje = '';

// Procesamiento de acciones CRUD con validación de emails únicos y control de NULL
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';
    // Normaliza los emails: si están vacíos, ponlos como null
    $email_institucional = isset($_POST['email_institucional']) && trim($_POST['email_institucional']) !== '' ? trim($_POST['email_institucional']) : null;
    $email_alterno = isset($_POST['email_alterno']) && trim($_POST['email_alterno']) !== '' ? trim($_POST['email_alterno']) : null;

    if ($accion === 'crear') {
        $datos = [
            'apellidos' => $_POST['apellidos'] ?? '',
            'nombres' => $_POST['nombres'] ?? '',
            'email_institucional' => $email_institucional,
            'email_alterno' => $email_alterno,
            'sexo' => $_POST['sexo'] ?? '',
            'estado_civil' => $_POST['estado_civil'] ?? '',
            'telefono' => $_POST['telefono'] ?? '',
            'celular' => $_POST['celular'] ?? ''
        ];
        // Validar duplicados solo si hay email
        if ($email_institucional && email_existe($email_institucional, 'email_institucional')) {
            $mensaje = "El email institucional ya está registrado.";
        } elseif ($email_alterno && email_existe($email_alterno, 'email_alterno')) {
            $mensaje = "El email alterno ya está registrado.";
        } else {
            $ok = crear_usuario($datos);
            $mensaje = $ok ? "Usuario creado correctamente." : "Error al crear el usuario.";
        }
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $datos = [
            'apellidos' => $_POST['apellidos'] ?? '',
            'nombres' => $_POST['nombres'] ?? '',
            'email_institucional' => $email_institucional,
            'email_alterno' => $email_alterno,
            'sexo' => $_POST['sexo'] ?? '',
            'estado_civil' => $_POST['estado_civil'] ?? '',
            'telefono' => $_POST['telefono'] ?? '',
            'celular' => $_POST['celular'] ?? ''
        ];
        // Validar duplicados (excluyendo el propio usuario)
        $duplicado = false;
        if ($email_institucional && email_existe($email_institucional, 'email_institucional', $id)) {
            $mensaje = "El email institucional ya está registrado por otro usuario.";
            $duplicado = true;
        }
        if (!$duplicado && $email_alterno && email_existe($email_alterno, 'email_alterno', $id)) {
            $mensaje = "El email alterno ya está registrado por otro usuario.";
            $duplicado = true;
        }
        if (!$duplicado) {
            $ok = actualizar_usuario($id, $datos);
            $mensaje = $ok ? "Usuario actualizado correctamente." : "Error al actualizar el usuario.";
        }
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $ok = eliminar_usuario($id);
        $mensaje = $ok ? "Usuario eliminado correctamente." : "Error al eliminar el usuario.";
    }
}

$usuarios = obtener_todos_los_usuarios();

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_usuario.php';
require_once BASE_PATH . 'backend/vistas/footer.php';
?>