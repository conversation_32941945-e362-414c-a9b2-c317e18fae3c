<?php

require_once dirname(__DIR__, 2) . '/config/config.php';
require_once BASE_PATH . 'publico/PHP/funciones.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    ir_a_pagina(DIRECCION_DEL_SITIO);
    exit();
}

// Ahora puedes usar las constantes
// echo DIRECCION_DEL_SITIO;
// echo BASE_PATH;

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/modelos/modelo_panel.php';
require_once BASE_PATH . 'backend/vistas/vista_panel.php';
require_once BASE_PATH . 'backend/vistas/footer.php';
?>