<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    header('Location: ' . DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_asignaturas.php';

$mensaje = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';

    if ($accion === 'crear') {
        $nombre = isset($_POST['nombre']) && trim($_POST['nombre']) !== '' ? trim($_POST['nombre']) : null;
        $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;

        if ($nombre) {
            $datos = [
                'nombre' => $nombre,
                'obs' => $obs
            ];
            $ok = crear_asignatura($datos);
            $mensaje = $ok ? "Asignatura creada correctamente." : "Error al crear la asignatura.";
        } else {
            $mensaje = "El nombre de la asignatura es requerido.";
        }
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $nombre = isset($_POST['nombre']) && trim($_POST['nombre']) !== '' ? trim($_POST['nombre']) : null;
        $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;

        if ($nombre) {
            $datos = [
                'nombre' => $nombre,
                'obs' => $obs
            ];
            $ok = actualizar_asignatura($id, $datos);
            $mensaje = $ok ? "Asignatura actualizada correctamente." : "Error al actualizar la asignatura.";
        } else {
            $mensaje = "El nombre de la asignatura es requerido.";
        }
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $ok = eliminar_asignatura($id);
        $mensaje = $ok ? "Asignatura eliminada correctamente." : "Error al eliminar la asignatura.";
    }
}

// Obtener datos para la vista
$asignaturas_en_sistema = obtener_asignaturas_en_sistema();
if (!is_array($asignaturas_en_sistema)) $asignaturas_en_sistema = [];

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_asignaturas.php';
require_once BASE_PATH . 'backend/vistas/footer.php';