<?php
require_once dirname(__DIR__, 2) . '/config/config.php';
require_once BASE_PATH . 'publico/PHP/funciones.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    ir_a_pagina(DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_estudiantes.php';

$mensaje = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';
    $usuario_id = isset($_POST['usuario_id']) && $_POST['usuario_id'] !== '' ? intval($_POST['usuario_id']) : null;
    $carrera_id = isset($_POST['carrera_id']) && $_POST['carrera_id'] !== '' ? intval($_POST['carrera_id']) : null;
    $obs = isset($_POST['obs']) ? trim($_POST['obs']) : null;

    if ($accion === 'crear') {
        $datos = [
            'usuario_id' => $usuario_id,
            'carrera_id' => $carrera_id,
            'obs' => $obs
        ];
        $ok = crear_estudiante($datos);
        if ($ok === 'duplicado') {
            $mensaje = "Ya existe un estudiante con el mismo usuario y carrera.";
        } else {
            $mensaje = $ok ? "Estudiante creado correctamente." : "Error al crear el estudiante.";
        }
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $datos = [
            'usuario_id' => $usuario_id,
            'carrera_id' => $carrera_id,
            'obs' => $obs
        ];
        $ok = actualizar_estudiante($id, $datos);
        if ($ok === 'duplicado') {
            $mensaje = "Ya existe un estudiante con el mismo usuario y carrera.";
        } else {
            $mensaje = $ok ? "Estudiante actualizado correctamente." : "Error al actualizar el estudiante.";
        }
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $ok = eliminar_estudiante($id);
        $mensaje = $ok ? "Estudiante eliminado correctamente." : "Error al eliminar el estudiante.";
    }
}

$estudiantes = obtener_todos_estudiantes();
$usuarios = obtener_usuarios_activos();
$carreras = obtener_carreras_activas();

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_estudiantes.php';
require_once BASE_PATH . 'backend/vistas/footer.php';
?>
