<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    header('Location: ' . DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_entidades.php';

$mensaje = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';

    if ($accion === 'crear') {
        $nombre = isset($_POST['nombre']) && trim($_POST['nombre']) !== '' ? trim($_POST['nombre']) : null;
        $tipo_entidad_id = isset($_POST['tipo_entidad_id']) && is_numeric($_POST['tipo_entidad_id']) ? intval($_POST['tipo_entidad_id']) : null;
        $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;

        if ($nombre && $tipo_entidad_id) {
            $datos = [
                'nombre' => $nombre,
                'tipo_entidad_id' => $tipo_entidad_id,
                'obs' => $obs
            ];
            $ok = crear_entidad($datos);
            $mensaje = $ok ? "Entidad creada correctamente." : "Error al crear la entidad.";
        } else {
            $mensaje = "El nombre y el tipo de entidad son requeridos.";
        }
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $nombre = isset($_POST['nombre']) && trim($_POST['nombre']) !== '' ? trim($_POST['nombre']) : null;
        $tipo_entidad_id = isset($_POST['tipo_entidad_id']) && is_numeric($_POST['tipo_entidad_id']) ? intval($_POST['tipo_entidad_id']) : null;
        $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;

        if ($nombre && $tipo_entidad_id) {
            $datos = [
                'nombre' => $nombre,
                'tipo_entidad_id' => $tipo_entidad_id,
                'obs' => $obs
            ];
            $ok = actualizar_entidad($id, $datos);
            $mensaje = $ok ? "Entidad actualizada correctamente." : "Error al actualizar la entidad.";
        } else {
            $mensaje = "El nombre y el tipo de entidad son requeridos.";
        }
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $ok = eliminar_entidad($id);
        $mensaje = $ok ? "Entidad eliminada correctamente." : "Error al eliminar la entidad.";
    }
}

// Obtener datos para la vista
$entidades_en_sistema = obtener_entidades_en_sistema();
$tipos_entidades_para_select = obtener_tipos_entidades_para_select();
if (!is_array($entidades_en_sistema)) $entidades_en_sistema = [];
if (!is_array($tipos_entidades_para_select)) $tipos_entidades_para_select = [];

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_entidades.php';
require_once BASE_PATH . 'backend/vistas/footer.php';