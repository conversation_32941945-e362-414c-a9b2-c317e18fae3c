<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    header('Location: ' . DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_roles.php';

$mensaje = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';
    $nombre = isset($_POST['nombre']) && trim($_POST['nombre']) !== '' ? trim($_POST['nombre']) : null;
    $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;

    if ($accion === 'crear') {
        if ($nombre && !rol_existe($nombre)) {
            $ok = crear_rol($nombre, $obs);
            $mensaje = $ok ? "Rol creado correctamente." : "Error al crear el rol.";
        } else {
            $mensaje = "El nombre del rol ya existe o es inválido.";
        }
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        if ($nombre && !rol_existe($nombre, $id)) {
            $ok = actualizar_rol($id, $nombre, $obs);
            $mensaje = $ok ? "Rol actualizado correctamente." : "Error al actualizar el rol.";
        } else {
            $mensaje = "El nombre del rol ya existe o es inválido.";
        }
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $ok = eliminar_rol($id);
        $mensaje = $ok ? "Rol eliminado correctamente." : "Error al eliminar el rol.";
    }
}


$roles_en_sistema = obtener_roles_en_sistema();


if (!is_array($roles_en_sistema)) $roles_en_sistema = [];

require_once BASE_PATH . 'backend/vistas/header.php';


require_once BASE_PATH . 'backend/vistas/vista_roles.php';
require_once BASE_PATH . 'backend/vistas/footer.php';