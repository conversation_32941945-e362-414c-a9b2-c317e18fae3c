<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    header('Location: ' . DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_auditoria_sesiones.php';

$mensaje = '';

// Obtener filtros de la URL
$usuario_id_filtro = $_GET['usuario_id'] ?? null;
$tipo_evento_filtro = $_GET['tipo_evento'] ?? null;
$fecha_desde_filtro = $_GET['fecha_desde'] ?? null;
$fecha_hasta_filtro = $_GET['fecha_hasta'] ?? null;

// Obtener datos para la vista
$sesiones_registros = obtener_sesiones_filtradas($usuario_id_filtro, $tipo_evento_filtro, $fecha_desde_filtro, $fecha_hasta_filtro);
$estadisticas_sesiones = obtener_estadisticas_sesiones($fecha_desde_filtro, $fecha_hasta_filtro);

// Obtener usuarios para el filtro
$usuarios_para_filtro = obtener_usuarios_para_filtro_sesiones();

if (!is_array($sesiones_registros)) $sesiones_registros = [];
if (!is_array($estadisticas_sesiones)) $estadisticas_sesiones = [];
if (!is_array($usuarios_para_filtro)) $usuarios_para_filtro = [];

/**
 * Obtener usuarios activos para el filtro
 */
function obtener_usuarios_para_filtro_sesiones() {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $sql = "SELECT 
                u.id,
                CONCAT(COALESCE(u.nombres, ''), ' ', COALESCE(u.apellidos, '')) as nombre_completo
            FROM usuarios u
            WHERE u.activo = 'S' AND u.eliminado = 'N'
            ORDER BY u.nombres, u.apellidos";
    
    $result = $conexion->query($sql);
    $usuarios = [];
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $usuarios[] = $row;
        }
    }
    
    $conexion->close();
    return $usuarios;
}

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_auditoria_sesiones.php';
require_once BASE_PATH . 'backend/vistas/footer.php';
?>
