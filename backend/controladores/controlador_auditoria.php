<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    header('Location: ' . DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_auditoria.php';

$mensaje = '';

/**
 * Obtener registros de auditoría con filtros
 */
function obtener_auditoria_filtrada($tabla = null, $operacion = null, $fecha_desde = null, $fecha_hasta = null) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    if ($conexion->connect_errno) return [];

    $where_conditions = ["a.activo = 'S'", "a.eliminado = 'N'"];
    
    if ($tabla && !empty($tabla)) {
        $tabla = $conexion->real_escape_string($tabla);
        $where_conditions[] = "a.tabla = '$tabla'";
    }
    
    if ($operacion && !empty($operacion)) {
        $operacion = $conexion->real_escape_string($operacion);
        $where_conditions[] = "a.operacion = '$operacion'";
    }
    
    if ($fecha_desde && !empty($fecha_desde)) {
        $fecha_desde = $conexion->real_escape_string($fecha_desde);
        $where_conditions[] = "DATE(a.fecha_hora) >= '$fecha_desde'";
    }
    
    if ($fecha_hasta && !empty($fecha_hasta)) {
        $fecha_hasta = $conexion->real_escape_string($fecha_hasta);
        $where_conditions[] = "DATE(a.fecha_hora) <= '$fecha_hasta'";
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "SELECT
                a.id,
                a.usuario_id,
                a.tabla,
                a.operacion,
                a.registro_id,
                a.datos_anteriores,
                a.datos_nuevos,
                a.fecha_hora,
                a.ip,
                a.user_agent,
                a.obs,
                CONCAT(COALESCE(u.nombres, ''), ' ', COALESCE(u.apellidos, '')) as usuario_nombre
            FROM auditoria_crud a
            LEFT JOIN usuarios u ON a.usuario_id = u.id
            WHERE $where_clause
            ORDER BY a.fecha_hora DESC
            LIMIT 1000"; // Limitar a 1000 registros para rendimiento
    
    $result = $conexion->query($sql);
    $registros = [];
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $registros[] = $row;
        }
    }
    
    $conexion->close();
    return $registros;
}

// Obtener filtros de la URL
$tabla_filtro = $_GET['tabla'] ?? null;
$operacion_filtro = $_GET['operacion'] ?? null;
$fecha_desde_filtro = $_GET['fecha_desde'] ?? null;
$fecha_hasta_filtro = $_GET['fecha_hasta'] ?? null;

// Obtener datos para la vista
$auditoria_registros = obtener_auditoria_filtrada($tabla_filtro, $operacion_filtro, $fecha_desde_filtro, $fecha_hasta_filtro);

if (!is_array($auditoria_registros)) $auditoria_registros = [];

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_auditoria.php';
require_once BASE_PATH . 'backend/vistas/footer.php';
?>
