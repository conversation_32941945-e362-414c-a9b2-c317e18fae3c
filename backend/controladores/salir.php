<?php
session_start();

// Configurar constantes antes de incluir modelos
if ( $_SERVER['HTTP_HOST'] == 'opedag.sited21.net' )
{
    define( "DIRECCION_DEL_SITIO" , "https://opedag.sited21.net/" );
}
else
{
    define( "DIRECCION_DEL_SITIO" , "http://localhost/opedag/" );
}

define( "BASE_PATH", dirname(__DIR__, 2) . '/' );
define( "SERVIDOR", "localhost" );
define( "USUARIO", "root" );
define( "CLAVE", "" );
define( "BD_SISTEMA", "opedag" );

// Incluir modelo de auditoría de sesiones
require_once BASE_PATH . 'backend/modelos/modelo_auditoria_sesiones.php';

// Registrar salida antes de destruir la sesión
if (isset($_SESSION['usuario_id']) && is_numeric($_SESSION['usuario_id'])) {
    registrar_salida_sesion($_SESSION['usuario_id'], 'Logout manual del usuario');
}

// Destruir todas las variables de sesión
$_SESSION = [];

// Eliminar la cookie de sesión si existe
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destruir la sesión
session_destroy();


// Redirigir al inicio de sesión o página principal
header("Location: " . DIRECCION_DEL_SITIO . "/index.php");
exit;