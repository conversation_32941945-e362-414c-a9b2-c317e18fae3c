<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once dirname(__DIR__, 2) . '/config/config.php';
require_once BASE_PATH . 'publico/PHP/funciones.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    ir_a_pagina(DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_equipos.php';

$mensaje = '';

// Manejar solicitudes AJAX para obtener datos de equipo
if (isset($_GET['ajax']) && $_GET['ajax'] === 'obtener_equipo' && isset($_GET['id'])) {
    header('Content-Type: application/json');
    $id = intval($_GET['id']);
    $equipo = obtener_equipo_por_id($id);
    echo json_encode($equipo);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';

    if ($accion === 'crear') {
        $nombre = isset($_POST['nombre']) ? trim($_POST['nombre']) : '';
        $obs = isset($_POST['obs']) ? trim($_POST['obs']) : '';

        if (empty($nombre)) {
            $mensaje = "El nombre del equipo es obligatorio.";
        } else {
            $datos = [
                'nombre' => $nombre,
                'obs' => $obs
            ];
            $resultado = crear_equipo($datos);
            if ($resultado === 'duplicado') {
                $mensaje = "Ya existe un equipo con ese nombre.";
            } elseif ($resultado) {
                $mensaje = "Equipo creado correctamente.";
            } else {
                $mensaje = "Error al crear el equipo.";
            }
        }
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $nombre = isset($_POST['nombre']) ? trim($_POST['nombre']) : '';
        $obs = isset($_POST['obs']) ? trim($_POST['obs']) : '';

        if (empty($nombre)) {
            $mensaje = "El nombre del equipo es obligatorio.";
        } else {
            $datos = [
                'nombre' => $nombre,
                'obs' => $obs
            ];
            $resultado = actualizar_equipo($id, $datos);
            if ($resultado === 'duplicado') {
                $mensaje = "Ya existe un equipo con ese nombre.";
            } elseif ($resultado) {
                $mensaje = "Equipo actualizado correctamente.";
            } else {
                $mensaje = "Error al actualizar el equipo.";
            }
        }
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $resultado = eliminar_equipo($id);
        if ($resultado) {
            $mensaje = "Equipo eliminado correctamente.";
        } else {
            $mensaje = "Error al eliminar el equipo.";
        }
    }
}

$equipos = obtener_todos_equipos();

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_equipos.php';
require_once BASE_PATH . 'backend/vistas/footer.php';
?>