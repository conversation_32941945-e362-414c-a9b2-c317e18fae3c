<?php
require_once dirname(__DIR__, 2) . '/config/config.php';
require_once BASE_PATH . 'publico/PHP/funciones.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    ir_a_pagina(DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_equipos.php';

$mensaje = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';
    $nombre = isset($_POST['nombre']) ? trim($_POST['nombre']) : null;
    $obs = isset($_POST['obs']) ? trim($_POST['obs']) : null;

    if ($accion === 'crear') {
        $datos = [
            'nombre' => $nombre,
            'obs' => $obs
        ];
        $ok = crear_equipo($datos);
        if ($ok === 'duplicado') {
            $mensaje = "Ya existe un equipo con ese nombre.";
        } else {
            $mensaje = $ok ? "Equipo creado correctamente." : "Error al crear el equipo.";
        }
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $datos = [
            'nombre' => $nombre,
            'obs' => $obs
        ];
        $ok = actualizar_equipo($id, $datos);
        if ($ok === 'duplicado') {
            $mensaje = "Ya existe un equipo con ese nombre.";
        } else {
            $mensaje = $ok ? "Equipo actualizado correctamente." : "Error al actualizar el equipo.";
        }
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $ok = eliminar_equipo($id);
        $mensaje = $ok ? "Equipo eliminado correctamente." : "Error al eliminar el equipo.";
    }
}

$equipos = obtener_todos_equipos();

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_equipos.php';
require_once BASE_PATH . 'backend/vistas/footer.php';
?>