<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    header('Location: ' . DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_olimpiada.php';

$mensaje = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';
    
    if ($accion === 'crear') {
        $nombre = isset($_POST['nombre']) && trim($_POST['nombre']) !== '' ? trim($_POST['nombre']) : null;
        $Interna = isset($_POST['Interna']) && trim($_POST['Interna']) !== '' ? trim($_POST['Interna']) : 'S';
        $fecha_desde = isset($_POST['fecha_desde']) && trim($_POST['fecha_desde']) !== '' ? trim($_POST['fecha_desde']) : null;
        $fecha_hasta = isset($_POST['fecha_hasta']) && trim($_POST['fecha_hasta']) !== '' ? trim($_POST['fecha_hasta']) : null;
        $detalles = isset($_POST['detalles']) && trim($_POST['detalles']) !== '' ? trim($_POST['detalles']) : null;
        $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;

        if ($nombre) {
            $datos = [
                'nombre' => $nombre,
                'Interna' => $Interna,
                'fecha_desde' => $fecha_desde,
                'fecha_hasta' => $fecha_hasta,
                'detalles' => $detalles,
                'obs' => $obs
            ];
            $ok = crear_olimpiada($datos);
            $mensaje = $ok ? "Olimpiada creada correctamente." : "Error al crear la olimpiada.";
        } else {
            $mensaje = "El nombre de la olimpiada es requerido.";
        }
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $nombre = isset($_POST['nombre']) && trim($_POST['nombre']) !== '' ? trim($_POST['nombre']) : null;
        $Interna = isset($_POST['Interna']) && trim($_POST['Interna']) !== '' ? trim($_POST['Interna']) : 'S';
        $fecha_desde = isset($_POST['fecha_desde']) && trim($_POST['fecha_desde']) !== '' ? trim($_POST['fecha_desde']) : null;
        $fecha_hasta = isset($_POST['fecha_hasta']) && trim($_POST['fecha_hasta']) !== '' ? trim($_POST['fecha_hasta']) : null;
        $detalles = isset($_POST['detalles']) && trim($_POST['detalles']) !== '' ? trim($_POST['detalles']) : null;
        $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;

        if ($nombre) {
            $datos = [
                'nombre' => $nombre,
                'Interna' => $Interna,
                'fecha_desde' => $fecha_desde,
                'fecha_hasta' => $fecha_hasta,
                'detalles' => $detalles,
                'obs' => $obs
            ];
            $ok = actualizar_olimpiada($id, $datos);
            $mensaje = $ok ? "Olimpiada actualizada correctamente." : "Error al actualizar la olimpiada.";
        } else {
            $mensaje = "El nombre de la olimpiada es requerido.";
        }
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $ok = eliminar_olimpiada($id);
        $mensaje = $ok ? "Olimpiada eliminada correctamente." : "Error al eliminar la olimpiada.";
    }
}

// Obtener datos para la vista
$olimpiadas_en_sistema = obtener_olimpiadas_en_sistema();
if (!is_array($olimpiadas_en_sistema)) $olimpiadas_en_sistema = [];

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_olimpiada.php';
require_once BASE_PATH . 'backend/vistas/footer.php';
