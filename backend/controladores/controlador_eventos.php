<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    header('Location: ' . DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_eventos.php';

$mensaje = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';
    
    if ($accion === 'crear') {
        $evento = isset($_POST['evento']) && trim($_POST['evento']) !== '' ? trim($_POST['evento']) : null;
        $descripcion = isset($_POST['descripcion']) && trim($_POST['descripcion']) !== '' ? trim($_POST['descripcion']) : null;
        $tipo_evento = isset($_POST['tipo_evento']) && is_numeric($_POST['tipo_evento']) ? intval($_POST['tipo_evento']) : null;
        $fecha_desde = isset($_POST['fecha_desde']) && trim($_POST['fecha_desde']) !== '' ? trim($_POST['fecha_desde']) : null;
        $hora_desde = isset($_POST['hora_desde']) && trim($_POST['hora_desde']) !== '' ? trim($_POST['hora_desde']) : null;
        $fecha_hasta = isset($_POST['fecha_hasta']) && trim($_POST['fecha_hasta']) !== '' ? trim($_POST['fecha_hasta']) : null;
        $hora_hasta = isset($_POST['hora_hasta']) && trim($_POST['hora_hasta']) !== '' ? trim($_POST['hora_hasta']) : null;
        $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;
        
        if ($evento) {
            $datos = [
                'evento' => $evento,
                'descripcion' => $descripcion,
                'tipo_evento' => $tipo_evento,
                'fecha_desde' => $fecha_desde,
                'hora_desde' => $hora_desde,
                'fecha_hasta' => $fecha_hasta,
                'hora_hasta' => $hora_hasta,
                'obs' => $obs
            ];
            
            $ok = crear_evento($datos);
            $mensaje = $ok ? "Evento creado correctamente." : "Error al crear el evento.";
        } else {
            $mensaje = "El nombre del evento es requerido.";
        }
        
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $evento = isset($_POST['evento']) && trim($_POST['evento']) !== '' ? trim($_POST['evento']) : null;
        $descripcion = isset($_POST['descripcion']) && trim($_POST['descripcion']) !== '' ? trim($_POST['descripcion']) : null;
        $tipo_evento = isset($_POST['tipo_evento']) && is_numeric($_POST['tipo_evento']) ? intval($_POST['tipo_evento']) : null;
        $fecha_desde = isset($_POST['fecha_desde']) && trim($_POST['fecha_desde']) !== '' ? trim($_POST['fecha_desde']) : null;
        $hora_desde = isset($_POST['hora_desde']) && trim($_POST['hora_desde']) !== '' ? trim($_POST['hora_desde']) : null;
        $fecha_hasta = isset($_POST['fecha_hasta']) && trim($_POST['fecha_hasta']) !== '' ? trim($_POST['fecha_hasta']) : null;
        $hora_hasta = isset($_POST['hora_hasta']) && trim($_POST['hora_hasta']) !== '' ? trim($_POST['hora_hasta']) : null;
        $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;
        
        if ($evento) {
            $datos = [
                'evento' => $evento,
                'descripcion' => $descripcion,
                'tipo_evento' => $tipo_evento,
                'fecha_desde' => $fecha_desde,
                'hora_desde' => $hora_desde,
                'fecha_hasta' => $fecha_hasta,
                'hora_hasta' => $hora_hasta,
                'obs' => $obs
            ];
            
            $ok = actualizar_evento($id, $datos);
            $mensaje = $ok ? "Evento actualizado correctamente." : "Error al actualizar el evento.";
        } else {
            $mensaje = "El nombre del evento es requerido.";
        }
        
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $ok = eliminar_evento($id);
        $mensaje = $ok ? "Evento eliminado correctamente." : "Error al eliminar el evento.";
    }
}

// Obtener datos para la vista
$eventos_en_sistema = obtener_eventos_en_sistema();
$tipos_eventos_para_select = obtener_tipos_eventos_para_select();

if (!is_array($eventos_en_sistema)) $eventos_en_sistema = [];
if (!is_array($tipos_eventos_para_select)) $tipos_eventos_para_select = [];

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_eventos.php';
require_once BASE_PATH . 'backend/vistas/footer.php';
?>
