<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    header('Location: ' . DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_tipos_entidades.php';

$mensaje = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';

    if ($accion === 'crear') {
        $nombre = isset($_POST['nombre']) && trim($_POST['nombre']) !== '' ? trim($_POST['nombre']) : null;
        $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;

        if ($nombre) {
            $datos = [
                'nombre' => $nombre,
                'obs' => $obs
            ];
            $ok = crear_tipo_entidad($datos);
            $mensaje = $ok ? "Tipo de entidad creado correctamente." : "Error al crear el tipo de entidad.";
        } else {
            $mensaje = "El nombre del tipo de entidad es requerido.";
        }
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $nombre = isset($_POST['nombre']) && trim($_POST['nombre']) !== '' ? trim($_POST['nombre']) : null;
        $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;

        if ($nombre) {
            $datos = [
                'nombre' => $nombre,
                'obs' => $obs
            ];
            $ok = actualizar_tipo_entidad($id, $datos);
            $mensaje = $ok ? "Tipo de entidad actualizado correctamente." : "Error al actualizar el tipo de entidad.";
        } else {
            $mensaje = "El nombre del tipo de entidad es requerido.";
        }
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        $ok = eliminar_tipo_entidad($id);
        $mensaje = $ok ? "Tipo de entidad eliminado correctamente." : "Error al eliminar el tipo de entidad.";
    }
}

// Obtener datos para la vista
$tipos_entidades_en_sistema = obtener_tipos_entidades_en_sistema();
if (!is_array($tipos_entidades_en_sistema)) $tipos_entidades_en_sistema = [];

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_tipos_entidades.php';
require_once BASE_PATH . 'backend/vistas/footer.php';