<?php
require_once dirname(__DIR__, 2) . '/config/config.php';
require_once BASE_PATH . 'publico/PHP/funciones.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    ir_a_pagina(DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_perfil.php';

$id_usuario = $_SESSION['id_usuario'] ?? 0;
$mensaje = '';
$usuario = obtener_usuario_por_id($id_usuario);

if ($_SERVER['REQUEST_METHOD'] === 'POST' && $usuario) {
    $datos = [
        'apellidos' => trim($_POST['apellidos'] ?? ''),
        'nombres' => trim($_POST['nombres'] ?? ''),
        'email_institucional' => trim($_POST['email_institucional'] ?? ''),
        'email_alterno' => trim($_POST['email_alterno'] ?? ''),
        'sexo' => $_POST['sexo'] ?? '',
        'estado_civil' => $_POST['estado_civil'] ?? '',
        'telefono' => trim($_POST['telefono'] ?? ''),
        'celular' => trim($_POST['celular'] ?? '')
    ];
    // Validación simple
    if ($datos['apellidos'] && $datos['nombres'] && $datos['email_institucional']) {
        if (actualizar_usuario($id_usuario, $datos)) {
            $mensaje = "Datos actualizados correctamente.";
            $usuario = obtener_usuario_por_id($id_usuario); // Refrescar datos
        } else {
            $mensaje = "Error al actualizar los datos.";
        }
    } else {
        $mensaje = "Por favor, complete todos los campos obligatorios.";
    }
}

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_perfil.php';
require_once BASE_PATH . 'backend/vistas/footer.php';
?>