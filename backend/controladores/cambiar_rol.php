<?php
if (session_status() === PHP_SESSION_NONE) session_start();

// Verificar que el usuario esté logueado
if (!isset($_SESSION['id_usuario'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Usuario no autenticado']);
    exit;
}

// Verificar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

// Obtener el rol enviado
$rol_solicitado = isset($_POST['rol']) ? trim($_POST['rol']) : '';

if (empty($rol_solicitado)) {
    echo json_encode(['success' => false, 'message' => 'Rol no especificado']);
    exit;
}

try {
    // Incluir configuración de base de datos
    require_once '../../config/config.php';
    
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    
    if ($conexion->connect_error) {
        throw new Exception('Error de conexión a la base de datos');
    }
    
    $id_usuario = intval($_SESSION['id_usuario']);
    
    // Verificar que el usuario tenga asignado el rol solicitado y obtener su ID
    $sql_verificar = "SELECT r.id, r.nombre 
                     FROM roles_por_usuario ru
                     INNER JOIN roles r ON ru.rol_id = r.id
                     WHERE ru.usuario_id = ? 
                         AND r.nombre = ?
                         AND ru.activo = 'S' AND ru.eliminado = 'N'
                         AND r.activo = 'S' AND r.eliminado = 'N'
                     LIMIT 1";
    
    $stmt = $conexion->prepare($sql_verificar);
    $stmt->bind_param("is", $id_usuario, $rol_solicitado);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'No tienes permisos para usar este rol']);
        exit;
    }
    
    $rol_data = $result->fetch_assoc();
    
    // Actualizar el rol y su ID en la sesión
    $_SESSION['rol_actual'] = $rol_solicitado;
    $_SESSION['rol_id_actual'] = $rol_data['id'];
    
    $conexion->close();
    
    echo json_encode(['success' => true, 'message' => 'Rol cambiado exitosamente']);
    
} catch (Exception $e) {
    error_log('Error en cambiar_rol.php: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error interno del servidor']);
}
?>