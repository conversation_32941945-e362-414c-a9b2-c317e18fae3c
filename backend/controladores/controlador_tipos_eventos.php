<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once dirname(__DIR__, 2) . '/config/config.php';

if (session_status() === PHP_SESSION_NONE) session_start();

if (!isset($_SESSION['usuario_autenticado']) || $_SESSION['usuario_autenticado'] !== "S") {
    header('Location: ' . DIRECCION_DEL_SITIO);
    exit();
}

require_once BASE_PATH . 'backend/modelos/modelo_tipos_eventos.php';

$mensaje = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';
    $nombre = isset($_POST['nombre']) && trim($_POST['nombre']) !== '' ? trim($_POST['nombre']) : null;
    $obs = isset($_POST['obs']) && trim($_POST['obs']) !== '' ? trim($_POST['obs']) : null;

    if ($accion === 'crear') {
        if ($nombre && !tipo_evento_existe($nombre)) {
            $ok = crear_tipo_evento($nombre, $obs);
            $mensaje = $ok ? "Tipo de evento creado correctamente." : "Error al crear el tipo de evento.";
        } else {
            $mensaje = "El nombre del tipo de evento ya existe o es inválido.";
        }
        
    } elseif ($accion === 'editar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        if ($nombre && !tipo_evento_existe($nombre, $id)) {
            $ok = actualizar_tipo_evento($id, $nombre, $obs);
            $mensaje = $ok ? "Tipo de evento actualizado correctamente." : "Error al actualizar el tipo de evento.";
        } else {
            $mensaje = "El nombre del tipo de evento ya existe o es inválido.";
        }
        
    } elseif ($accion === 'eliminar' && isset($_POST['id'])) {
        $id = intval($_POST['id']);
        
        // Verificar si el tipo de evento está en uso
        if (tipo_evento_en_uso($id)) {
            $mensaje = "No se puede eliminar este tipo de evento porque está siendo utilizado en otros registros.";
        } else {
            $ok = eliminar_tipo_evento($id);
            $mensaje = $ok ? "Tipo de evento eliminado correctamente." : "Error al eliminar el tipo de evento.";
        }
    }
}

// Obtener datos para la vista
$tipos_eventos_en_sistema = obtener_tipos_eventos_en_sistema();

if (!is_array($tipos_eventos_en_sistema)) $tipos_eventos_en_sistema = [];

require_once BASE_PATH . 'backend/vistas/header.php';
require_once BASE_PATH . 'backend/vistas/vista_tipos_eventos.php';
require_once BASE_PATH . 'backend/vistas/footer.php';
?>
