<div class="container mt-4">
    <h3>Gestión de Usuarios y Roles</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info"><?php echo htmlspecialchars($mensaje); ?></div>
    <?php endif; ?>

    <!-- Botón para abrir modal de nueva asignación -->
    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#modalUsuarioRolNuevo">
        <i class="bi bi-plus"></i> Nueva Asignación
    </button>

    <table id="tablaUsuariosRoles" class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Apellidos</th>
                <th>Nombres</th>
                <th>Email Institucional</th>
                <th>Rol</th>
                <th>Observaciones</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <th>ID</th>
                <th>Apellidos</th>
                <th>Nombres</th>
                <th>Email Institucional</th>
                <th>Rol</th>
                <th>Observaciones</th>
                <th></th>
            </tr>
        </tfoot>
        <tbody>
        <?php
        if (isset($usuarios_roles) && is_array($usuarios_roles) && count($usuarios_roles) > 0) {
            foreach ($usuarios_roles as $ur) {
                if (is_array($ur)) {
                    $id = $ur['id'] ?? '';
                    $usuario_id = $ur['usuario_id'] ?? '';
                    $rol_id = $ur['rol_id'] ?? '';
                    $apellidos = $ur['apellidos'] ?? '';
                    $nombres = $ur['nombres'] ?? '';
                    $email_institucional = $ur['email_institucional'] ?? '';
                    $rol_nombre = $ur['rol_nombre'] ?? '';
                    $obs = $ur['obs'] ?? '';
                    ?>
                    <tr>
                        <td><?php echo htmlspecialchars($id); ?></td>
                        <td><?php echo htmlspecialchars($apellidos); ?></td>
                        <td><?php echo htmlspecialchars($nombres); ?></td>
                        <td><?php echo htmlspecialchars($email_institucional); ?></td>
                        <td><?php echo htmlspecialchars($rol_nombre); ?></td>
                        <td><?php echo htmlspecialchars($obs); ?></td>
                        <td>
                            <!-- Botón editar -->
                            <button class="btn btn-primary btn-sm btn-editar" 
                                data-id="<?php echo htmlspecialchars($id); ?>"
                                data-usuario_id="<?php echo htmlspecialchars($usuario_id); ?>"
                                data-rol_id="<?php echo htmlspecialchars($rol_id); ?>"
                                data-apellidos="<?php echo htmlspecialchars($apellidos); ?>"
                                data-nombres="<?php echo htmlspecialchars($nombres); ?>"
                                data-rol_nombre="<?php echo htmlspecialchars($rol_nombre); ?>"
                                data-obs="<?php echo htmlspecialchars($obs); ?>"
                                data-bs-toggle="modal" data-bs-target="#modalUsuarioRolEditar">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <!-- Botón eliminar -->
                            <form method="post" style="display:inline;">
                                <input type="hidden" name="id" value="<?php echo htmlspecialchars($id); ?>">
                                <input type="hidden" name="accion" value="eliminar">
                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Eliminar asignación?');">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    <?php
                }
            }
        }
        ?>
        </tbody>
    </table>
</div>

<!-- Modal Nueva Asignación -->
<div class="modal fade" id="modalUsuarioRolNuevo" tabindex="-1" aria-labelledby="modalUsuarioRolNuevoLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formNuevoUsuarioRol">
      <input type="hidden" name="accion" value="crear">
      <div class="modal-header">
        <h5 class="modal-title" id="modalUsuarioRolNuevoLabel">Nueva Asignación Usuario-Rol</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="row g-3">
            <div class="col-md-12">
                <label class="form-label">Usuario</label>
                <select id="usuario_id_nuevo" name="usuario_id" class="form-select" required>
                    <option value="">Seleccione un usuario</option>
                    <?php foreach ($usuarios_para_select as $usuario): ?>
                        <option value="<?php echo $usuario['id']; ?>">
                            <?php echo htmlspecialchars($usuario['apellidos'] . ', ' . $usuario['nombres'] . ' (' . $usuario['email_institucional'] . ')'); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-12">
                <label class="form-label">Rol</label>
                <select id="rol_id_nuevo" name="rol_id" class="form-select" required>
                    <option value="">Seleccione un rol</option>
                    <?php foreach ($roles_para_select as $rol): ?>
                        <option value="<?php echo $rol['id']; ?>">
                            <?php echo htmlspecialchars($rol['nombre']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-12">
                <label class="form-label">Observaciones</label>
                <textarea id="obs_nuevo" name="obs" class="form-control" rows="3"></textarea>
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-success">Crear Asignación</button>
      </div>
    </form>
  </div>
</div>

<!-- Modal Editar Asignación -->
<div class="modal fade" id="modalUsuarioRolEditar" tabindex="-1" aria-labelledby="modalUsuarioRolEditarLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formEditarUsuarioRol">
      <input type="hidden" name="accion" value="editar">
      <input type="hidden" name="id" id="editar_id">
      <div class="modal-header">
        <h5 class="modal-title" id="modalUsuarioRolEditarLabel">Editar Asignación Usuario-Rol</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="row g-3">
            <div class="col-md-12">
                <label class="form-label">Usuario</label>
                <select id="usuario_id_editar" name="usuario_id" class="form-select" required>
                    <option value="">Seleccione un usuario</option>
                    <?php foreach ($usuarios_para_select as $usuario): ?>
                        <option value="<?php echo $usuario['id']; ?>">
                            <?php echo htmlspecialchars($usuario['apellidos'] . ', ' . $usuario['nombres'] . ' (' . $usuario['email_institucional'] . ')'); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-12">
                <label class="form-label">Rol</label>
                <select id="rol_id_editar" name="rol_id" class="form-select" required>
                    <option value="">Seleccione un rol</option>
                    <?php foreach ($roles_para_select as $rol): ?>
                        <option value="<?php echo $rol['id']; ?>">
                            <?php echo htmlspecialchars($rol['nombre']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-12">
                <label class="form-label">Observaciones</label>
                <textarea id="obs_editar" name="obs" class="form-control" rows="3"></textarea>
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary">Actualizar Asignación</button>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar DataTable
    var table = $('#tablaUsuariosRoles').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
        },
        lengthMenu: [ [5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"] ],
        pageLength: 25, // Valor por defecto: 25 registros por página
        dom: 'lfrtip',
        initComplete: function () {
            this.api().columns().every(function () {
                var column = this;
                if (column.index() === 6) return; // Excluir columna de acciones
                var input = document.createElement("input");
                input.className = "form-control form-control-sm";
                $(input).appendTo($(column.footer()).empty())
                .on('keyup change clear', function () {
                    if (column.search() !== this.value) {
                        column.search(this.value).draw();
                    }
                });
            });
        }
    });

    // Limpiar formulario de nueva asignación al abrir modal
    $('#modalUsuarioRolNuevo').on('show.bs.modal', function () {
        $('#formNuevoUsuarioRol')[0].reset();
    });

    // Rellenar modal de edición
    document.querySelectorAll('.btn-editar').forEach(btn => {
        btn.addEventListener('click', function() {
            document.getElementById('editar_id').value = this.dataset.id;
            document.getElementById('usuario_id_editar').value = this.dataset.usuario_id;
            document.getElementById('rol_id_editar').value = this.dataset.rol_id;
            document.getElementById('obs_editar').value = this.dataset.obs;
        });
    });
});
</script>
