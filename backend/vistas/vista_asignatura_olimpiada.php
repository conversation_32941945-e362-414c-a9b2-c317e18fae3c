<div class="container mt-4">
    <h3>Gestión de Asignatura-Olimpiada</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info"><?php echo htmlspecialchars($mensaje); ?></div>
    <?php endif; ?>

    <!-- Botón para abrir modal de nuevo registro -->
    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#modalNuevo">
        <i class="bi bi-plus"></i> Nuevo Registro
    </button>

    <table id="tablaAsignaturaOlimpiada" class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Asignatura</th>
                <th>Olimpiada</th>
                <th>Obs</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <th>ID</th>
                <th>Asignatura</th>
                <th>Olimpiada</th>
                <th>Obs</th>
                <th></th>
            </tr>
        </tfoot>
        <tbody>
        <?php foreach ($asignatura_olimpiada as $ao): ?>
            <tr>
                <td><?php echo $ao['id']; ?></td>
                <td><?php echo htmlspecialchars($ao['asignatura_nombre'] ?? ''); ?></td>
                <td><?php echo htmlspecialchars($ao['olimpiada_nombre'] ?? ''); ?></td>
                <td><?php echo htmlspecialchars($ao['obs'] ?? ''); ?></td>
                <td>
                    <!-- Botón editar -->
                    <button class="btn btn-primary btn-sm btn-editar"
                        data-id="<?php echo $ao['id']; ?>"
                        data-asignatura_id="<?php echo $ao['asignatura_id']; ?>"
                        data-olimpiada_id="<?php echo $ao['olimpiada_id']; ?>"
                        data-obs="<?php echo htmlspecialchars($ao['obs'] ?? ''); ?>"
                        data-bs-toggle="modal" data-bs-target="#modalEditar">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <!-- Botón eliminar -->
                    <form method="post" style="display:inline;">
                        <input type="hidden" name="id" value="<?php echo $ao['id']; ?>">
                        <input type="hidden" name="accion" value="eliminar">
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Eliminar registro?');">
                            <i class="bi bi-trash"></i>
                        </button>
                    </form>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>

<!-- Modal Nuevo -->
<div class="modal fade" id="modalNuevo" tabindex="-1" aria-labelledby="modalNuevoLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formNuevo">
      <input type="hidden" name="accion" value="crear">
      <div class="modal-header">
        <h5 class="modal-title" id="modalNuevoLabel">Nuevo Registro</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
            <label class="form-label">Asignatura</label>
            <select name="asignatura_id" class="form-select" required>
                <option value="">Seleccione</option>
                <?php foreach ($asignaturas as $a): ?>
                    <option value="<?php echo $a['id']; ?>"><?php echo htmlspecialchars($a['nombre']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Olimpiada</label>
            <select name="olimpiada_id" class="form-select" required>
                <option value="">Seleccione</option>
                <?php foreach ($olimpiadas as $o): ?>
                    <option value="<?php echo $o['id']; ?>"><?php echo htmlspecialchars($o['nombre']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Observaciones</label>
            <textarea name="obs" class="form-control"></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-success">Crear</button>
      </div>
    </form>
  </div>
</div>

<!-- Modal Editar -->
<div class="modal fade" id="modalEditar" tabindex="-1" aria-labelledby="modalEditarLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formEditar">
      <input type="hidden" name="accion" value="editar">
      <input type="hidden" name="id" id="editar_id">
      <div class="modal-header">
        <h5 class="modal-title" id="modalEditarLabel">Editar Registro</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
            <label class="form-label">Asignatura</label>
            <select name="asignatura_id" class="form-select" id="editar_asignatura_id" required>
                <option value="">Seleccione</option>
                <?php foreach ($asignaturas as $a): ?>
                    <option value="<?php echo $a['id']; ?>"><?php echo htmlspecialchars($a['nombre']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Olimpiada</label>
            <select name="olimpiada_id" class="form-select" id="editar_olimpiada_id" required>
                <option value="">Seleccione</option>
                <?php foreach ($olimpiadas as $o): ?>
                    <option value="<?php echo $o['id']; ?>"><?php echo htmlspecialchars($o['nombre']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Observaciones</label>
            <textarea name="obs" class="form-control" id="editar_obs"></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary">Actualizar</button>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar DataTable
    var table = $('#tablaAsignaturaOlimpiada').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
        },
        lengthMenu: [ [5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"] ],
        pageLength: 25,
        dom: 'lfrtip',
        initComplete: function () {
            this.api().columns().every(function () {
                var column = this;
                if (column.index() === 4) return;
                var input = document.createElement("input");
                input.className = "form-control form-control-sm";
                $(input).appendTo($(column.footer()).empty())
                .on('keyup change clear', function () {
                    if (column.search() !== this.value) {
                        column.search(this.value).draw();
                    }
                });
            });
        }
    });

    // Limpiar formulario de nuevo registro al abrir modal
    $('#modalNuevo').on('show.bs.modal', function () {
        $('#formNuevo')[0].reset();
    });

    // Rellenar modal de edición
    document.querySelectorAll('.btn-editar').forEach(btn => {
        btn.addEventListener('click', function() {
            document.getElementById('editar_id').value = this.dataset.id;
            document.getElementById('editar_asignatura_id').value = this.dataset.asignatura_id;
            document.getElementById('editar_olimpiada_id').value = this.dataset.olimpiada_id;
            document.getElementById('editar_obs').value = this.dataset.obs;
        });
    });
});
</script>
