<div class="container mt-4">
    <h3>Gestión de Carreras</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info"><?php echo htmlspecialchars($mensaje); ?></div>
    <?php endif; ?>

    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#modalCarreraNueva">
        <i class="bi bi-plus"></i> Nueva Carrera
    </button>

    <table id="tablaCarreras" class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Nombre</th>
                <th>Entidad</th>
                <th>Observaciones</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <th>ID</th>
                <th>Nombre</th>
                <th>Entidad</th>
                <th>Observaciones</th>
                <th></th>
            </tr>
        </tfoot>
        <tbody>
        <?php
        if (isset($carreras_en_sistema) && is_array($carreras_en_sistema) && count($carreras_en_sistema) > 0) {
            foreach ($carreras_en_sistema as $carrera) {
                if (is_array($carrera)) {
                    $id = $carrera['id'] ?? '';
                    $nombre = $carrera['nombre'] ?? '';
                    $entidad_id = $carrera['entidad_id'] ?? '';
                    $entidad_nombre = $carrera['entidad_nombre'] ?? '';
                    $obs = $carrera['obs'] ?? '';
                    ?>
                    <tr>
                        <td><?php echo htmlspecialchars($id); ?></td>
                        <td><?php echo htmlspecialchars($nombre); ?></td>
                        <td><?php echo htmlspecialchars($entidad_nombre); ?></td>
                        <td><?php echo htmlspecialchars(substr($obs, 0, 40)) . (strlen($obs) > 40 ? '...' : ''); ?></td>
                        <td>
                            <button class="btn btn-primary btn-sm btn-editar"
                                data-id="<?php echo htmlspecialchars($id); ?>"
                                data-nombre="<?php echo htmlspecialchars($nombre); ?>"
                                data-entidad_id="<?php echo htmlspecialchars($entidad_id); ?>"
                                data-obs="<?php echo htmlspecialchars($obs); ?>"
                                data-bs-toggle="modal" data-bs-target="#modalCarreraEditar">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <form method="post" style="display:inline;">
                                <input type="hidden" name="id" value="<?php echo htmlspecialchars($id); ?>">
                                <input type="hidden" name="accion" value="eliminar">
                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Eliminar carrera?');">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    <?php
                }
            }
        }
        ?>
        </tbody>
    </table>
</div>

<!-- Modal Nueva Carrera -->
<div class="modal fade" id="modalCarreraNueva" tabindex="-1" aria-labelledby="modalCarreraNuevaLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formNuevaCarrera">
      <input type="hidden" name="accion" value="crear">
      <div class="modal-header">
        <h5 class="modal-title" id="modalCarreraNuevaLabel">Nueva Carrera</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
            <label class="form-label">Nombre</label>
            <input type="text" id="nombre_nuevo" name="nombre" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Entidad</label>
            <select id="entidad_id_nuevo" name="entidad_id" class="form-select" required>
                <option value="">Seleccione...</option>
                <?php foreach ($entidades_para_select as $entidad): ?>
                    <option value="<?php echo $entidad['id']; ?>">
                        <?php echo htmlspecialchars($entidad['nombre']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Observaciones</label>
            <textarea id="obs_nuevo" name="obs" class="form-control" rows="2"></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-success">Crear</button>
      </div>
    </form>
  </div>
</div>

<!-- Modal Editar Carrera -->
<div class="modal fade" id="modalCarreraEditar" tabindex="-1" aria-labelledby="modalCarreraEditarLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formEditarCarrera">
      <input type="hidden" name="accion" value="editar">
      <input type="hidden" name="id" id="editar_id">
      <div class="modal-header">
        <h5 class="modal-title" id="modalCarreraEditarLabel">Editar Carrera</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
            <label class="form-label">Nombre</label>
            <input type="text" id="nombre_editar" name="nombre" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Entidad</label>
            <select id="entidad_id_editar" name="entidad_id" class="form-select" required>
                <option value="">Seleccione...</option>
                <?php foreach ($entidades_para_select as $entidad): ?>
                    <option value="<?php echo $entidad['id']; ?>">
                        <?php echo htmlspecialchars($entidad['nombre']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Observaciones</label>
            <textarea id="obs_editar" name="obs" class="form-control" rows="2"></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary">Actualizar</button>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var table = $('#tablaCarreras').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
        },
        lengthMenu: [ [5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"] ],
        pageLength: 25,
        dom: 'lfrtip',
        scrollX: true,
        initComplete: function () {
            this.api().columns().every(function () {
                var column = this;
                if (column.index() === 4) return;
                var input = document.createElement("input");
                input.className = "form-control form-control-sm";
                $(input).appendTo($(column.footer()).empty())
                .on('keyup change clear', function () {
                    if (column.search() !== this.value) {
                        column.search(this.value).draw();
                    }
                });
            });
        }
    });

    $('#modalCarreraNueva').on('show.bs.modal', function () {
        $('#formNuevaCarrera')[0].reset();
    });

    document.querySelectorAll('.btn-editar').forEach(btn => {
        btn.addEventListener('click', function() {
            document.getElementById('editar_id').value = this.dataset.id;
            document.getElementById('nombre_editar').value = this.dataset.nombre;
            document.getElementById('entidad_id_editar').value = this.dataset.entidad_id;
            document.getElementById('obs_editar').value = this.dataset.obs;
        });
    });
});
</script>