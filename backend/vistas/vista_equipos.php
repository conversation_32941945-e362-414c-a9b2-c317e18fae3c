<div class="container mt-4">
    <h3>Gestión de Equipos</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info"><?php echo htmlspecialchars($mensaje); ?></div>
    <?php endif; ?>

    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#modalNuevoEquipo">
        <i class="bi bi-plus"></i> Nuevo Equipo
    </button>

    <table id="tablaEquipos" class="table table-striped table-bordered" style="width:100%">
        <thead>
            <tr>
                <th>ID</th>
                <th>Nombre</th>
                <th>Observaciones</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <th>ID</th>
                <th>Nombre</th>
                <th>Observaciones</th>
                <th></th>
            </tr>
        </tfoot>
        <tbody>
        <?php foreach ($equipos as $e): ?>
            <tr>
                <td><?php echo $e['id']; ?></td>
                <td><?php echo htmlspecialchars($e['nombre'] ?? ''); ?></td>
                <td><?php echo htmlspecialchars($e['obs'] ?? ''); ?></td>
                <td>
                    <button class="btn btn-primary btn-sm btn-editar"
                        data-id="<?php echo $e['id']; ?>"
                        data-nombre="<?php echo htmlspecialchars($e['nombre'] ?? ''); ?>"
                        data-obs="<?php echo htmlspecialchars($e['obs'] ?? ''); ?>"
                        data-bs-toggle="modal" data-bs-target="#modalEditarEquipo">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <form method="post" style="display:inline;">
                        <input type="hidden" name="id" value="<?php echo $e['id']; ?>">
                        <input type="hidden" name="accion" value="eliminar">
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Eliminar equipo?');">
                            <i class="bi bi-trash"></i>
                        </button>
                    </form>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>

<!-- Modal Nuevo Equipo -->
<div class="modal fade" id="modalNuevoEquipo" tabindex="-1" aria-labelledby="modalNuevoEquipoLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formNuevoEquipo">
      <input type="hidden" name="accion" value="crear">
      <div class="modal-header">
        <h5 class="modal-title" id="modalNuevoEquipoLabel">Nuevo Equipo</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
            <label class="form-label">Nombre</label>
            <input type="text" name="nombre" class="form-control" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Observaciones</label>
            <textarea name="obs" class="form-control"></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-success">Crear</button>
      </div>
    </form>
  </div>
</div>

<!-- Modal Editar Equipo -->
<div class="modal fade" id="modalEditarEquipo" tabindex="-1" aria-labelledby="modalEditarEquipoLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formEditarEquipo">
      <input type="hidden" name="accion" value="editar">
      <input type="hidden" name="id" id="editar_id">
      <div class="modal-header">
        <h5 class="modal-title" id="modalEditarEquipoLabel">Editar Equipo</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
            <label class="form-label">Nombre</label>
            <input type="text" name="nombre" class="form-control" id="editar_nombre" required>
        </div>
        <div class="mb-3">
            <label class="form-label">Observaciones</label>
            <textarea name="obs" class="form-control" id="editar_obs"></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary">Actualizar</button>
      </div>
    </form>
  </div>
</div>

<script>
$(document).ready(function() {
    $('#tablaEquipos').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
        },
        lengthMenu: [ [5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"] ],
        pageLength: 25,
        dom: 'lfrtip'
    });

    $('#modalNuevoEquipo').on('show.bs.modal', function () {
        $('#formNuevoEquipo')[0].reset();
    });

    document.querySelectorAll('.btn-editar').forEach(btn => {
        btn.addEventListener('click', function() {
            document.getElementById('editar_id').value = this.dataset.id;
            document.getElementById('editar_nombre').value = this.dataset.nombre;
            document.getElementById('editar_obs').value = this.dataset.obs;
        });
    });
});
</script>