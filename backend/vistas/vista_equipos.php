<div class="container mt-4">
    <h3>Gestión de Equipos</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($mensaje); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#modalNuevoEquipo">
        <i class="bi bi-plus"></i> Nuevo Equipo
    </button>

    <table id="tablaEquipos" class="table table-striped table-bordered" style="width:100%">
        <thead>
            <tr>
                <th>ID</th>
                <th>Nombre</th>
                <th>Observaciones</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <th>ID</th>
                <th>Nombre</th>
                <th>Observaciones</th>
                <th></th>
            </tr>
        </tfoot>
        <tbody>
        <?php
        if (isset($equipos) && is_array($equipos) && count($equipos) > 0) {
            foreach ($equipos as $equipo) {
                if (is_array($equipo)) {
                    $id = $equipo['id'] ?? '';
                    $nombre = $equipo['nombre'] ?? '';
                    $obs = $equipo['obs'] ?? '';
                    ?>
                    <tr>
                        <td><?php echo htmlspecialchars($id); ?></td>
                        <td><?php echo htmlspecialchars($nombre); ?></td>
                        <td><?php echo htmlspecialchars($obs); ?></td>
                        <td>
                            <button class="btn btn-primary btn-sm btn-editar"
                                data-id="<?php echo htmlspecialchars($id); ?>"
                                data-nombre="<?php echo htmlspecialchars($nombre); ?>"
                                data-obs="<?php echo htmlspecialchars($obs); ?>"
                                data-bs-toggle="modal" data-bs-target="#modalEditarEquipo">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <form method="post" style="display:inline;" onsubmit="return confirm('¿Está seguro de eliminar este equipo?');">
                                <input type="hidden" name="id" value="<?php echo htmlspecialchars($id); ?>">
                                <input type="hidden" name="accion" value="eliminar">
                                <button type="submit" class="btn btn-danger btn-sm">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    <?php
                }
            }
        }
        ?>
        </tbody>
    </table>
</div>

<!-- Modal Nuevo Equipo -->
<div class="modal fade" id="modalNuevoEquipo" tabindex="-1" aria-labelledby="modalNuevoEquipoLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <form method="post" id="formNuevoEquipo">
        <input type="hidden" name="accion" value="crear">
        <div class="modal-header">
          <h5 class="modal-title" id="modalNuevoEquipoLabel">Nuevo Equipo</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
              <label for="nuevo_nombre" class="form-label">Nombre <span class="text-danger">*</span></label>
              <input type="text" name="nombre" id="nuevo_nombre" class="form-control" required maxlength="100">
          </div>
          <div class="mb-3">
              <label for="nuevo_obs" class="form-label">Observaciones</label>
              <textarea name="obs" id="nuevo_obs" class="form-control" rows="3"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="submit" class="btn btn-success">
            <i class="bi bi-check"></i> Crear
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal Editar Equipo -->
<div class="modal fade" id="modalEditarEquipo" tabindex="-1" aria-labelledby="modalEditarEquipoLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <form method="post" id="formEditarEquipo">
        <input type="hidden" name="accion" value="editar">
        <input type="hidden" name="id" id="editar_id">
        <div class="modal-header">
          <h5 class="modal-title" id="modalEditarEquipoLabel">Editar Equipo</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
              <label for="editar_nombre" class="form-label">Nombre <span class="text-danger">*</span></label>
              <input type="text" name="nombre" id="editar_nombre" class="form-control" required maxlength="100">
          </div>
          <div class="mb-3">
              <label for="editar_obs" class="form-label">Observaciones</label>
              <textarea name="obs" id="editar_obs" class="form-control" rows="3"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-check"></i> Actualizar
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar DataTable
    var table = $('#tablaEquipos').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
        },
        lengthMenu: [ [5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"] ],
        pageLength: 25,
        dom: 'lfrtip',
        scrollX: true,
        order: [[0, 'desc']], // Ordenar por ID descendente
        initComplete: function () {
            // Agregar filtros en el footer
            this.api().columns().every(function () {
                var column = this;
                if (column.index() === 3) return; // Excluir columna de acciones
                var input = document.createElement("input");
                input.className = "form-control form-control-sm";
                input.placeholder = "Buscar...";
                $(input).appendTo($(column.footer()).empty())
                .on('keyup change clear', function () {
                    if (column.search() !== this.value) {
                        column.search(this.value).draw();
                    }
                });
            });
        }
    });

    // Limpiar formulario al abrir modal de nuevo equipo
    $('#modalNuevoEquipo').on('show.bs.modal', function () {
        $('#formNuevoEquipo')[0].reset();
        $('#nuevo_nombre').focus();
    });

    // Manejar clic en botón editar
    $(document).on('click', '.btn-editar', function() {
        var id = $(this).data('id');
        var nombre = $(this).data('nombre');
        var obs = $(this).data('obs');

        // Llenar el formulario de edición
        $('#editar_id').val(id);
        $('#editar_nombre').val(nombre);
        $('#editar_obs').val(obs);

        // Enfocar el campo nombre
        $('#modalEditarEquipo').on('shown.bs.modal', function () {
            $('#editar_nombre').focus();
        });
    });

    // Validación de formularios
    $('#formNuevoEquipo').on('submit', function(e) {
        var nombre = $('#nuevo_nombre').val().trim();
        if (nombre === '') {
            e.preventDefault();
            alert('El nombre del equipo es obligatorio.');
            $('#nuevo_nombre').focus();
            return false;
        }
    });

    $('#formEditarEquipo').on('submit', function(e) {
        var nombre = $('#editar_nombre').val().trim();
        if (nombre === '') {
            e.preventDefault();
            alert('El nombre del equipo es obligatorio.');
            $('#editar_nombre').focus();
            return false;
        }
    });

    // Auto-cerrar alertas después de 5 segundos
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
});
</script>