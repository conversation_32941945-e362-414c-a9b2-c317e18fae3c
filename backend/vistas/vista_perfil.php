<div class="container mt-4">
    <h3>Mi Perfil</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info"><?php echo limpieza_de_campo($mensaje); ?></div>
    <?php endif; ?>
    <?php if ($usuario): ?>
    <form method="post" class="row g-3">
        <div class="col-md-6">
            <label class="form-label">Apellidos</label>
            <input type="text" id="apellidos" name="apellidos" class="form-control" value="<?php echo limpieza_de_campo($usuario['apellidos']); ?>" required>
        </div>
        <div class="col-md-6">
            <label class="form-label">Nombres</label>
            <input type="text" id="nombres" name="nombres" class="form-control" value="<?php echo limpieza_de_campo($usuario['nombres']); ?>" required>
        </div>
        <div class="col-md-6">
            <label class="form-label">Email institucional</label>
            <input type="email" id="email_institucional" name="email_institucional" class="form-control" value="<?php echo limpieza_de_campo($usuario['email_institucional']); ?>" required>
        </div>
        <div class="col-md-6">
            <label class="form-label">Email alterno</label>
            <input type="email" id="email_alterno" name="email_alterno" class="form-control" value="<?php echo limpieza_de_campo($usuario['email_alterno']); ?>">
        </div>
        <div class="col-md-3">
            <label class="form-label">Sexo</label>
            <select id="sexo" name="sexo" class="form-select">
                <option value="">Seleccione</option>
                <option value="M" <?php if($usuario['sexo']=='M') echo 'selected'; ?>>Masculino</option>
                <option value="F" <?php if($usuario['sexo']=='F') echo 'selected'; ?>>Femenino</option>
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">Estado civil</label>
            <select id="estado_civil" name="estado_civil" class="form-select">
                <option value="">Seleccione</option>
                <option value="Soltero(a)" <?php if($usuario['estado_civil']=='Soltero(a)') echo 'selected'; ?>>Soltero(a)</option>
                <option value="Casado(a)" <?php if($usuario['estado_civil']=='Casado(a)') echo 'selected'; ?>>Casado(a)</option>
                <option value="Viudo(a)" <?php if($usuario['estado_civil']=='Viudo(a)') echo 'selected'; ?>>Viudo(a)</option>
                <option value="Divorciado(a)" <?php if($usuario['estado_civil']=='Divorciado(a)') echo 'selected'; ?>>Divorciado(a)</option>
                <option value="UnionLibre" <?php if($usuario['estado_civil']=='UnionLibre') echo 'selected'; ?>>Unión Libre</option>
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">Teléfono</label>
            <input type="text" id="telefono" name="telefono" class="form-control" value="<?php echo limpieza_de_campo($usuario['telefono']); ?>">
        </div>
        <div class="col-md-3">
            <label class="form-label">Celular</label>
            <input type="text" id="celular" name="celular" class="form-control" value="<?php echo limpieza_de_campo($usuario['celular']); ?>">
        </div>
        <div class="col-12">
            <button id="boton_actualizar" name="boton_actualizar" type="submit" class="btn btn-primary">Actualizar</button>
        </div>
    </form>
    <?php else: ?>
        <div class="alert alert-danger">No se encontró el usuario.</div>
    <?php endif; ?>
</div>