
<div class="container mt-5">
    <h2>Gestión de Roles</h2>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info"><?= htmlspecialchars($mensaje) ?></div>
    <?php endif; ?>
    <form id="form-rol" method="post" class="row g-3 mb-4">
        <input type="hidden" name="accion" id="accion" value="crear">
        <input type="hidden" name="id" id="id" value="">
        <div class="col-md-5">
            <label for="nombre" class="form-label">Nombre del Rol</label>
            <input type="text" class="form-control" id="nombre" name="nombre" required>
        </div>
        <div class="col-md-5">
            <label for="obs" class="form-label">Observaciones</label>
            <input type="text" class="form-control" id="obs" name="obs">
        </div>
        <div class="col-md-2 d-flex align-items-end">
            <button type="submit" class="btn btn-success me-2" id="btn-guardar">Guardar</button>
            <button type="button" class="btn btn-secondary" id="btn-cancelar">Cancelar</button>
        </div>
    </form>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Nombre</th>
                <th>Observaciones</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tbody>
        <?php
        if (isset($roles_en_sistema) && is_array($roles_en_sistema) && count($roles_en_sistema) > 0) {
            foreach ($roles_en_sistema as $rol) {
                if (is_array($rol) && isset($rol['id']) && isset($rol['nombre'])) {
                    $id = $rol['id'] ?? '';
                    $nombre = $rol['nombre'] ?? '';
                    $obs = $rol['obs'] ?? '';
                    ?>
                    <tr>
                        <td><?= htmlspecialchars($id) ?></td>
                        <td><?= htmlspecialchars($nombre) ?></td>
                        <td><?= htmlspecialchars($obs) ?></td>
                        <td>
                            <button class='btn btn-sm btn-primary btn-editar' data-id='<?= htmlspecialchars($id) ?>' data-nombre='<?= htmlspecialchars($nombre, ENT_QUOTES) ?>' data-obs='<?= htmlspecialchars($obs, ENT_QUOTES) ?>'>Editar</button>
                            <button class='btn btn-sm btn-danger btn-eliminar' data-id='<?= htmlspecialchars($id) ?>'>Eliminar</button>
                        </td>
                    </tr>
                    <?php
                }
            }
        } else {
            echo "<tr><td colspan='4' class='text-center'>No hay roles registrados.</td></tr>";
        }
        ?>
        </tbody>
    </table>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.btn-editar').forEach(function(btn) {
        btn.addEventListener('click', function() {
            document.getElementById('accion').value = 'editar';
            document.getElementById('id').value = this.dataset.id || '';
            document.getElementById('nombre').value = this.dataset.nombre || '';
            document.getElementById('obs').value = this.dataset.obs || '';
        });
    });
    document.querySelectorAll('.btn-eliminar').forEach(function(btn) {
        btn.addEventListener('click', function() {
            if (confirm('¿Está seguro de eliminar este rol?')) {
                document.getElementById('accion').value = 'eliminar';
                document.getElementById('id').value = this.dataset.id || '';
                document.getElementById('form-rol').submit();
            }
        });
    });
    document.getElementById('btn-cancelar').addEventListener('click', function() {
        document.getElementById('accion').value = 'crear';
        document.getElementById('id').value = '';
        document.getElementById('nombre').value = '';
        document.getElementById('obs').value = '';
    });
});
</script>