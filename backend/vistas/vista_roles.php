
<div class="container mt-4">
    <h3>Gestión de Roles</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info"><?php echo htmlspecialchars($mensaje); ?></div>
    <?php endif; ?>

    <!-- Botón para abrir modal de nuevo rol -->
    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#modalRolNuevo">
        <i class="bi bi-plus"></i> Nuevo Rol
    </button>

    <table id="tablaRoles" class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Nombre</th>
                <th>Observaciones</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <th>ID</th>
                <th>Nombre</th>
                <th>Observaciones</th>
                <th></th>
            </tr>
        </tfoot>
        <tbody>
        <?php
        if (isset($roles_en_sistema) && is_array($roles_en_sistema) && count($roles_en_sistema) > 0) {
            foreach ($roles_en_sistema as $rol) {
                if (is_array($rol) && isset($rol['id']) && isset($rol['nombre'])) {
                    $id = $rol['id'] ?? '';
                    $nombre = $rol['nombre'] ?? '';
                    $obs = $rol['obs'] ?? '';
                    ?>
                    <tr>
                        <td><?php echo htmlspecialchars($id); ?></td>
                        <td><?php echo htmlspecialchars($nombre); ?></td>
                        <td><?php echo htmlspecialchars($obs); ?></td>
                        <td>
                            <!-- Botón editar -->
                            <button class="btn btn-primary btn-sm btn-editar"
                                data-id="<?php echo htmlspecialchars($id); ?>"
                                data-nombre="<?php echo htmlspecialchars($nombre); ?>"
                                data-obs="<?php echo htmlspecialchars($obs); ?>"
                                data-bs-toggle="modal" data-bs-target="#modalRolEditar">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <!-- Botón eliminar -->
                            <form method="post" style="display:inline;">
                                <input type="hidden" name="id" value="<?php echo htmlspecialchars($id); ?>">
                                <input type="hidden" name="accion" value="eliminar">
                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Eliminar rol?');">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    <?php
                }
            }
        }
        ?>
        </tbody>
    </table>
</div>

<!-- Modal Nuevo Rol -->
<div class="modal fade" id="modalRolNuevo" tabindex="-1" aria-labelledby="modalRolNuevoLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formNuevoRol">
      <input type="hidden" name="accion" value="crear">
      <div class="modal-header">
        <h5 class="modal-title" id="modalRolNuevoLabel">Nuevo Rol</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="row g-3">
            <div class="col-md-12">
                <label class="form-label">Nombre del Rol</label>
                <input type="text" id="nombre_nuevo" name="nombre" class="form-control" required>
            </div>
            <div class="col-md-12">
                <label class="form-label">Observaciones</label>
                <textarea id="obs_nuevo" name="obs" class="form-control" rows="3"></textarea>
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-success">Crear</button>
      </div>
    </form>
  </div>
</div>

<!-- Modal Editar Rol -->
<div class="modal fade" id="modalRolEditar" tabindex="-1" aria-labelledby="modalRolEditarLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formEditarRol">
      <input type="hidden" name="accion" value="editar">
      <input type="hidden" name="id" id="editar_id">
      <div class="modal-header">
        <h5 class="modal-title" id="modalRolEditarLabel">Editar Rol</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="row g-3">
            <div class="col-md-12">
                <label class="form-label">Nombre del Rol</label>
                <input type="text" id="nombre_editar" name="nombre" class="form-control" required>
            </div>
            <div class="col-md-12">
                <label class="form-label">Observaciones</label>
                <textarea id="obs_editar" name="obs" class="form-control" rows="3"></textarea>
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary">Actualizar</button>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar DataTable
    var table = $('#tablaRoles').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
        },
        lengthMenu: [ [5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"] ],
        dom: 'lfrtip',
        initComplete: function () {
            this.api().columns().every(function () {
                var column = this;
                if (column.index() === 3) return; // Excluir columna de acciones
                var input = document.createElement("input");
                input.className = "form-control form-control-sm";
                $(input).appendTo($(column.footer()).empty())
                .on('keyup change clear', function () {
                    if (column.search() !== this.value) {
                        column.search(this.value).draw();
                    }
                });
            });
        }
    });

    // Limpiar formulario de nuevo rol al abrir modal
    $('#modalRolNuevo').on('show.bs.modal', function () {
        $('#formNuevoRol')[0].reset();
    });

    // Rellenar modal de edición
    document.querySelectorAll('.btn-editar').forEach(btn => {
        btn.addEventListener('click', function() {
            document.getElementById('editar_id').value = this.dataset.id;
            document.getElementById('nombre_editar').value = this.dataset.nombre;
            document.getElementById('obs_editar').value = this.dataset.obs;
        });
    });
});
</script>