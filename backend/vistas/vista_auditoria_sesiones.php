<?php
/**
 * Formatear fecha y hora para mostrar
 */
function formatear_fecha_hora_sesion($fecha_hora) {
    if (empty($fecha_hora)) return '';
    return date('d/m/Y H:i:s', strtotime($fecha_hora));
}

/**
 * Formatear tipo de evento para mostrar con colores
 */
function formatear_tipo_evento($tipo_evento) {
    $colores = [
        'INGRESO' => 'success',
        'SALIDA' => 'danger'
    ];
    
    $iconos = [
        'INGRESO' => 'bi-box-arrow-in-right',
        'SALIDA' => 'bi-box-arrow-right'
    ];
    
    $color = $colores[$tipo_evento] ?? 'secondary';
    $icono = $iconos[$tipo_evento] ?? 'bi-circle';
    
    return "<span class='badge bg-$color'><i class='$icono'></i> $tipo_evento</span>";
}
?>

<div class="container mt-4">
    <h3>Auditoría de Sesiones</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info"><?php echo htmlspecialchars($mensaje); ?></div>
    <?php endif; ?>

    <!-- Estadísticas rápidas -->
    <?php if (isset($estadisticas_sesiones) && !empty($estadisticas_sesiones)): ?>
    <div class="row mb-4">
        <?php foreach ($estadisticas_sesiones as $stat): ?>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo $stat['tipo_evento']; ?></h5>
                    <p class="card-text">
                        <strong><?php echo $stat['total']; ?></strong> eventos<br>
                        <small class="text-muted"><?php echo $stat['usuarios_unicos']; ?> usuarios únicos</small>
                    </p>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>

    <!-- Filtros -->
    <div class="card mb-3">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Usuario</label>
                    <select name="usuario_id" class="form-select">
                        <option value="">Todos los usuarios</option>
                        <?php if (isset($usuarios_para_filtro) && is_array($usuarios_para_filtro)): ?>
                            <?php foreach ($usuarios_para_filtro as $usuario): ?>
                                <option value="<?php echo $usuario['id']; ?>" 
                                    <?php echo (isset($_GET['usuario_id']) && $_GET['usuario_id'] == $usuario['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($usuario['nombre_completo']); ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Tipo de Evento</label>
                    <select name="tipo_evento" class="form-select">
                        <option value="">Todos los tipos</option>
                        <option value="INGRESO" <?php echo (isset($_GET['tipo_evento']) && $_GET['tipo_evento'] === 'INGRESO') ? 'selected' : ''; ?>>INGRESO</option>
                        <option value="SALIDA" <?php echo (isset($_GET['tipo_evento']) && $_GET['tipo_evento'] === 'SALIDA') ? 'selected' : ''; ?>>SALIDA</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Fecha Desde</label>
                    <input type="date" name="fecha_desde" class="form-control" value="<?php echo $_GET['fecha_desde'] ?? ''; ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Fecha Hasta</label>
                    <input type="date" name="fecha_hasta" class="form-control" value="<?php echo $_GET['fecha_hasta'] ?? ''; ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block">Filtrar</button>
                </div>
                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <a href="?url=auditoria_sesiones" class="btn btn-secondary d-block">Limpiar</a>
                </div>
            </form>
        </div>
    </div>

    <table id="tablaSesiones" class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Usuario</th>
                <th>Tipo de Evento</th>
                <th>Fecha/Hora</th>
                <th>IP</th>
                <th>Navegador</th>
                <th>Observaciones</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <th>ID</th>
                <th>Usuario</th>
                <th>Tipo de Evento</th>
                <th>Fecha/Hora</th>
                <th>IP</th>
                <th>Navegador</th>
                <th>Observaciones</th>
                <th></th>
            </tr>
        </tfoot>
        <tbody>
        <?php
        if (isset($sesiones_registros) && is_array($sesiones_registros) && count($sesiones_registros) > 0) {
            foreach ($sesiones_registros as $registro) {
                if (is_array($registro)) {
                    $id = $registro['id'] ?? '';
                    $usuario_nombre = $registro['usuario_nombre'] ?? 'Usuario Desconocido';
                    $tipo_evento = $registro['tipo_evento'] ?? '';
                    $fecha_hora = $registro['fecha_hora'] ?? '';
                    $ip = $registro['ip'] ?? '';
                    $user_agent = $registro['user_agent'] ?? '';
                    $obs = $registro['obs'] ?? '';
                    
                    // Extraer navegador del user agent
                    $navegador = extraer_navegador($user_agent);
                    ?>
                    <tr>
                        <td><?php echo htmlspecialchars($id); ?></td>
                        <td><?php echo htmlspecialchars($usuario_nombre); ?></td>
                        <td><?php echo formatear_tipo_evento($tipo_evento); ?></td>
                        <td><?php echo formatear_fecha_hora_sesion($fecha_hora); ?></td>
                        <td><?php echo htmlspecialchars($ip); ?></td>
                        <td><?php echo htmlspecialchars($navegador); ?></td>
                        <td><?php echo htmlspecialchars(substr($obs, 0, 30)) . (strlen($obs) > 30 ? '...' : ''); ?></td>
                        <td>
                            <!-- Botón ver detalles -->
                            <button class="btn btn-info btn-sm btn-detalles" 
                                data-id="<?php echo htmlspecialchars($id); ?>"
                                data-usuario="<?php echo htmlspecialchars($usuario_nombre); ?>"
                                data-tipo_evento="<?php echo htmlspecialchars($tipo_evento); ?>"
                                data-fecha_hora="<?php echo htmlspecialchars($fecha_hora); ?>"
                                data-ip="<?php echo htmlspecialchars($ip); ?>"
                                data-user_agent="<?php echo htmlspecialchars($user_agent); ?>"
                                data-obs="<?php echo htmlspecialchars($obs); ?>"
                                data-bs-toggle="modal" data-bs-target="#modalDetallesSesion">
                                <i class="bi bi-eye"></i>
                            </button>
                        </td>
                    </tr>
                    <?php
                }
            }
        }
        ?>
        </tbody>
    </table>
</div>

<!-- Modal Detalles de Sesión -->
<div class="modal fade" id="modalDetallesSesion" tabindex="-1" aria-labelledby="modalDetallesSesionLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modalDetallesSesionLabel">Detalles de Sesión</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label"><strong>ID de Registro:</strong></label>
                <p id="detalle_id"></p>
            </div>
            <div class="col-md-6">
                <label class="form-label"><strong>Usuario:</strong></label>
                <p id="detalle_usuario"></p>
            </div>
            <div class="col-md-6">
                <label class="form-label"><strong>Tipo de Evento:</strong></label>
                <p id="detalle_tipo_evento"></p>
            </div>
            <div class="col-md-6">
                <label class="form-label"><strong>Fecha/Hora:</strong></label>
                <p id="detalle_fecha_hora"></p>
            </div>
            <div class="col-md-6">
                <label class="form-label"><strong>Dirección IP:</strong></label>
                <p id="detalle_ip"></p>
            </div>
            <div class="col-md-6">
                <label class="form-label"><strong>Navegador:</strong></label>
                <p id="detalle_navegador"></p>
            </div>
            <div class="col-md-12">
                <label class="form-label"><strong>User Agent Completo:</strong></label>
                <pre id="detalle_user_agent" class="bg-light p-2 rounded" style="font-size: 0.8em; word-wrap: break-word;"></pre>
            </div>
            <div class="col-md-12">
                <label class="form-label"><strong>Observaciones:</strong></label>
                <p id="detalle_obs"></p>
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
      </div>
    </div>
  </div>
</div>

<?php
/**
 * Extraer información del navegador del user agent
 */
function extraer_navegador($user_agent) {
    if (empty($user_agent)) return 'Desconocido';
    
    if (strpos($user_agent, 'Chrome') !== false) return 'Chrome';
    if (strpos($user_agent, 'Firefox') !== false) return 'Firefox';
    if (strpos($user_agent, 'Safari') !== false) return 'Safari';
    if (strpos($user_agent, 'Edge') !== false) return 'Edge';
    if (strpos($user_agent, 'Opera') !== false) return 'Opera';
    if (strpos($user_agent, 'Internet Explorer') !== false) return 'IE';
    
    return 'Otro';
}
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar DataTable
    var table = $('#tablaSesiones').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
        },
        lengthMenu: [ [5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"] ],
        pageLength: 25,
        dom: 'lfrtip',
        scrollX: true,
        order: [[0, 'desc']], // Ordenar por ID descendente (más recientes primero)
        initComplete: function () {
            this.api().columns().every(function () {
                var column = this;
                if (column.index() === 7) return; // Excluir columna de acciones
                var input = document.createElement("input");
                input.className = "form-control form-control-sm";
                $(input).appendTo($(column.footer()).empty())
                .on('keyup change clear', function () {
                    if (column.search() !== this.value) {
                        column.search(this.value).draw();
                    }
                });
            });
        }
    });

    // Rellenar modal de detalles
    document.querySelectorAll('.btn-detalles').forEach(btn => {
        btn.addEventListener('click', function() {
            document.getElementById('detalle_id').textContent = this.dataset.id;
            document.getElementById('detalle_usuario').textContent = this.dataset.usuario;
            document.getElementById('detalle_tipo_evento').innerHTML = formatearTipoEventoModal(this.dataset.tipo_evento);
            document.getElementById('detalle_fecha_hora').textContent = this.dataset.fecha_hora;
            document.getElementById('detalle_ip').textContent = this.dataset.ip;
            document.getElementById('detalle_navegador').textContent = extraerNavegadorJS(this.dataset.user_agent);
            document.getElementById('detalle_user_agent').textContent = this.dataset.user_agent;
            document.getElementById('detalle_obs').textContent = this.dataset.obs;
        });
    });
    
    function formatearTipoEventoModal(tipo_evento) {
        const colores = {
            'INGRESO': 'success',
            'SALIDA': 'danger'
        };
        const iconos = {
            'INGRESO': 'bi-box-arrow-in-right',
            'SALIDA': 'bi-box-arrow-right'
        };
        const color = colores[tipo_evento] || 'secondary';
        const icono = iconos[tipo_evento] || 'bi-circle';
        return `<span class="badge bg-${color}"><i class="${icono}"></i> ${tipo_evento}</span>`;
    }
    
    function extraerNavegadorJS(userAgent) {
        if (!userAgent) return 'Desconocido';
        if (userAgent.includes('Chrome')) return 'Chrome';
        if (userAgent.includes('Firefox')) return 'Firefox';
        if (userAgent.includes('Safari')) return 'Safari';
        if (userAgent.includes('Edge')) return 'Edge';
        if (userAgent.includes('Opera')) return 'Opera';
        if (userAgent.includes('Internet Explorer')) return 'IE';
        return 'Otro';
    }
});
</script>
