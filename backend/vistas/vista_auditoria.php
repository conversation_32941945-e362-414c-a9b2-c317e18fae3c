<?php
/**
 * Formatear fecha y hora para mostrar
 */
function formatear_fecha_hora($fecha_hora) {
    if (empty($fecha_hora)) return '';
    return date('d/m/Y H:i:s', strtotime($fecha_hora));
}

/**
 * Formatear operación para mostrar con colores
 */
function formatear_operacion($operacion) {
    $colores = [
        'CREATE' => 'success',
        'READ' => 'info', 
        'UPDATE' => 'warning',
        'DELETE' => 'danger'
    ];
    
    $color = $colores[$operacion] ?? 'secondary';
    return "<span class='badge bg-$color'>$operacion</span>";
}
?>

<div class="container mt-4">
    <h3>Auditoría del Sistema</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info"><?php echo htmlspecialchars($mensaje); ?></div>
    <?php endif; ?>

    <!-- Filtros -->
    <div class="card mb-3">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Tabla</label>
                    <select name="tabla" class="form-select">
                        <option value="">Todas las tablas</option>
                        <?php
                        if (isset($tablas_disponibles) && is_array($tablas_disponibles)) {
                            foreach ($tablas_disponibles as $tabla) {
                                $selected = (isset($_GET['tabla']) && $_GET['tabla'] === $tabla) ? 'selected' : '';
                                $tabla_nombre = ucfirst(str_replace('_', ' ', $tabla)); // Formatear nombre para mostrar
                                echo "<option value=\"" . htmlspecialchars($tabla) . "\" $selected>" . htmlspecialchars($tabla_nombre) . "</option>";
                            }
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Operación</label>
                    <select name="operacion" class="form-select">
                        <option value="">Todas las operaciones</option>
                        <option value="CREATE" <?php echo (isset($_GET['operacion']) && $_GET['operacion'] === 'CREATE') ? 'selected' : ''; ?>>CREATE</option>
                        <option value="READ" <?php echo (isset($_GET['operacion']) && $_GET['operacion'] === 'READ') ? 'selected' : ''; ?>>READ</option>
                        <option value="UPDATE" <?php echo (isset($_GET['operacion']) && $_GET['operacion'] === 'UPDATE') ? 'selected' : ''; ?>>UPDATE</option>
                        <option value="DELETE" <?php echo (isset($_GET['operacion']) && $_GET['operacion'] === 'DELETE') ? 'selected' : ''; ?>>DELETE</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Fecha Desde</label>
                    <input type="date" name="fecha_desde" class="form-control" value="<?php echo $_GET['fecha_desde'] ?? ''; ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Fecha Hasta</label>
                    <input type="date" name="fecha_hasta" class="form-control" value="<?php echo $_GET['fecha_hasta'] ?? ''; ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block">Filtrar</button>
                </div>
            </form>
        </div>
    </div>

    <table id="tablaAuditoria" class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Usuario</th>
                <th>Tabla</th>
                <th>Operación</th>
                <th>Registro ID</th>
                <th>Fecha/Hora</th>
                <th>IP</th>
                <th>Observaciones</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <th>ID</th>
                <th>Usuario</th>
                <th>Tabla</th>
                <th>Operación</th>
                <th>Registro ID</th>
                <th>Fecha/Hora</th>
                <th>IP</th>
                <th>Observaciones</th>
                <th></th>
            </tr>
        </tfoot>
        <tbody>
        <?php
        if (isset($auditoria_registros) && is_array($auditoria_registros) && count($auditoria_registros) > 0) {
            foreach ($auditoria_registros as $registro) {
                if (is_array($registro)) {
                    $id = $registro['id'] ?? '';
                    $usuario_nombre = $registro['usuario_nombre'] ?? 'Sistema';
                    $tabla = $registro['tabla'] ?? '';
                    $operacion = $registro['operacion'] ?? '';
                    $registro_id = $registro['registro_id'] ?? '';
                    $fecha_hora = $registro['fecha_hora'] ?? '';
                    $ip = $registro['ip'] ?? '';
                    $obs = $registro['obs'] ?? '';
                    $datos_anteriores = $registro['datos_anteriores'] ?? '';
                    $datos_nuevos = $registro['datos_nuevos'] ?? '';
                    ?>
                    <tr>
                        <td><?php echo htmlspecialchars($id); ?></td>
                        <td><?php echo htmlspecialchars($usuario_nombre); ?></td>
                        <td><?php echo htmlspecialchars($tabla); ?></td>
                        <td><?php echo formatear_operacion($operacion); ?></td>
                        <td><?php echo htmlspecialchars($registro_id); ?></td>
                        <td><?php echo formatear_fecha_hora($fecha_hora); ?></td>
                        <td><?php echo htmlspecialchars($ip); ?></td>
                        <td><?php echo htmlspecialchars(substr($obs, 0, 30)) . (strlen($obs) > 30 ? '...' : ''); ?></td>
                        <td>
                            <!-- Botón ver detalles -->
                            <button class="btn btn-info btn-sm btn-detalles" 
                                data-id="<?php echo htmlspecialchars($id); ?>"
                                data-tabla="<?php echo htmlspecialchars($tabla); ?>"
                                data-operacion="<?php echo htmlspecialchars($operacion); ?>"
                                data-registro_id="<?php echo htmlspecialchars($registro_id); ?>"
                                data-fecha_hora="<?php echo htmlspecialchars($fecha_hora); ?>"
                                data-usuario="<?php echo htmlspecialchars($usuario_nombre); ?>"
                                data-ip="<?php echo htmlspecialchars($ip); ?>"
                                data-obs="<?php echo htmlspecialchars($obs); ?>"
                                data-datos_anteriores="<?php echo htmlspecialchars($datos_anteriores); ?>"
                                data-datos_nuevos="<?php echo htmlspecialchars($datos_nuevos); ?>"
                                data-bs-toggle="modal" data-bs-target="#modalDetallesAuditoria">
                                <i class="bi bi-eye"></i>
                            </button>
                        </td>
                    </tr>
                    <?php
                }
            }
        }
        ?>
        </tbody>
    </table>
</div>

<!-- Modal Detalles de Auditoría -->
<div class="modal fade" id="modalDetallesAuditoria" tabindex="-1" aria-labelledby="modalDetallesAuditoriaLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modalDetallesAuditoriaLabel">Detalles de Auditoría</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label"><strong>ID de Auditoría:</strong></label>
                <p id="detalle_id"></p>
            </div>
            <div class="col-md-6">
                <label class="form-label"><strong>Usuario:</strong></label>
                <p id="detalle_usuario"></p>
            </div>
            <div class="col-md-4">
                <label class="form-label"><strong>Tabla:</strong></label>
                <p id="detalle_tabla"></p>
            </div>
            <div class="col-md-4">
                <label class="form-label"><strong>Operación:</strong></label>
                <p id="detalle_operacion"></p>
            </div>
            <div class="col-md-4">
                <label class="form-label"><strong>Registro ID:</strong></label>
                <p id="detalle_registro_id"></p>
            </div>
            <div class="col-md-6">
                <label class="form-label"><strong>Fecha/Hora:</strong></label>
                <p id="detalle_fecha_hora"></p>
            </div>
            <div class="col-md-6">
                <label class="form-label"><strong>IP:</strong></label>
                <p id="detalle_ip"></p>
            </div>
            <div class="col-md-12">
                <label class="form-label"><strong>Observaciones:</strong></label>
                <p id="detalle_obs"></p>
            </div>
            <div class="col-md-6" id="datos_anteriores_container">
                <label class="form-label"><strong>Datos Anteriores:</strong></label>
                <pre id="detalle_datos_anteriores" class="bg-light p-2 rounded"></pre>
            </div>
            <div class="col-md-6" id="datos_nuevos_container">
                <label class="form-label"><strong>Datos Nuevos:</strong></label>
                <pre id="detalle_datos_nuevos" class="bg-light p-2 rounded"></pre>
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar DataTable
    var table = $('#tablaAuditoria').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
        },
        lengthMenu: [ [5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"] ],
        pageLength: 25,
        dom: 'lfrtip',
        scrollX: true,
        order: [[0, 'desc']], // Ordenar por ID descendente (más recientes primero)
        initComplete: function () {
            this.api().columns().every(function () {
                var column = this;
                if (column.index() === 8) return; // Excluir columna de acciones
                var input = document.createElement("input");
                input.className = "form-control form-control-sm";
                $(input).appendTo($(column.footer()).empty())
                .on('keyup change clear', function () {
                    if (column.search() !== this.value) {
                        column.search(this.value).draw();
                    }
                });
            });
        }
    });

    // Rellenar modal de detalles
    document.querySelectorAll('.btn-detalles').forEach(btn => {
        btn.addEventListener('click', function() {
            document.getElementById('detalle_id').textContent = this.dataset.id;
            document.getElementById('detalle_usuario').textContent = this.dataset.usuario;
            document.getElementById('detalle_tabla').textContent = this.dataset.tabla;
            document.getElementById('detalle_operacion').innerHTML = formatearOperacionModal(this.dataset.operacion);
            document.getElementById('detalle_registro_id').textContent = this.dataset.registro_id;
            document.getElementById('detalle_fecha_hora').textContent = this.dataset.fecha_hora;
            document.getElementById('detalle_ip').textContent = this.dataset.ip;
            document.getElementById('detalle_obs').textContent = this.dataset.obs;
            
            // Formatear y mostrar datos JSON
            const datosAnteriores = this.dataset.datos_anteriores;
            const datosNuevos = this.dataset.datos_nuevos;
            
            if (datosAnteriores && datosAnteriores !== 'null') {
                try {
                    const jsonAnteriores = JSON.parse(datosAnteriores);
                    document.getElementById('detalle_datos_anteriores').textContent = JSON.stringify(jsonAnteriores, null, 2);
                    document.getElementById('datos_anteriores_container').style.display = 'block';
                } catch (e) {
                    document.getElementById('detalle_datos_anteriores').textContent = datosAnteriores;
                    document.getElementById('datos_anteriores_container').style.display = 'block';
                }
            } else {
                document.getElementById('datos_anteriores_container').style.display = 'none';
            }
            
            if (datosNuevos && datosNuevos !== 'null') {
                try {
                    const jsonNuevos = JSON.parse(datosNuevos);
                    document.getElementById('detalle_datos_nuevos').textContent = JSON.stringify(jsonNuevos, null, 2);
                    document.getElementById('datos_nuevos_container').style.display = 'block';
                } catch (e) {
                    document.getElementById('detalle_datos_nuevos').textContent = datosNuevos;
                    document.getElementById('datos_nuevos_container').style.display = 'block';
                }
            } else {
                document.getElementById('datos_nuevos_container').style.display = 'none';
            }
        });
    });
    
    function formatearOperacionModal(operacion) {
        const colores = {
            'CREATE': 'success',
            'READ': 'info',
            'UPDATE': 'warning', 
            'DELETE': 'danger'
        };
        const color = colores[operacion] || 'secondary';
        return `<span class="badge bg-${color}">${operacion}</span>`;
    }
});
</script>
