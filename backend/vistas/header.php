<?php
if (session_status() === PHP_SESSION_NONE) session_start();
function obtenerMenuPorRol($rol_nombre) 
{
    $base = defined('DIRECCION_DEL_SITIO') ? DIRECCION_DEL_SITIO : '/opedag';
    $menus = [
        'Admin' => [
            [
                'titulo' => 'Usuarios',
                'subopciones' => [
                    ['nombre' => 'Datos de Usuarios', 'enlace' => "$base/usuarios"],
                    ['nombre' => 'Roles', 'enlace' => "$base/roles"],
                    ['nombre' => 'Usuarios y Roles', 'enlace' => "$base/usuarios_roles"],
                ]
            ],
            [
                'titulo' => 'Agenda',
                'subopciones' => [
                    ['nombre' => 'Tipos de Eventos', 'enlace' => '$base/tipos_eventos'],
                    ['nombre' => 'Eventos', 'enlace' => '$base/eventos'],
                    
                ]
            ],
            [
                'titulo' => 'Datos Generales',
                'subopciones' => [
                    ['nombre' => 'Resumen', 'enlace' => '#'],
                    ['nombre' => 'Estadísticas', 'enlace' => '#'],
                ]
            ],
           
            [
                'titulo' => 'Configuración',
                'subopciones' => [
                    ['nombre' => 'Sistema', 'enlace' => '#'],
                    ['nombre' => 'Permisos', 'enlace' => '#'],
                    ['nombre' => 'Backup', 'enlace' => '#'],
                ]
            ]
        ],
        // ... el resto igual ...
    ];
    return isset($menus[$rol_nombre]) ? $menus[$rol_nombre] : [];
}

// Obtener datos del usuario y sus roles
$roles_usuario = []; // <-- Cambia aquí
$roles_data = [];
$usuario_nombre = '';
$rol_actual = '';
$rol_id_actual = 0;
$sidebar_menu = [];
$mensaje_sin_roles = '';

if (isset($_SESSION['id_usuario'])) {
    $conexion = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
    $id_usuario = intval($_SESSION['id_usuario']);

    // Obtener datos del usuario activo
    $sql_usuario = "SELECT apellidos, nombres FROM usuarios WHERE id = $id_usuario AND activo = 'S' AND eliminado = 'N' LIMIT 1";
    $result_usuario = $conexion->query($sql_usuario);
    if ($row_usuario = $result_usuario->fetch_assoc()) {
        $usuario_nombre = htmlspecialchars($row_usuario['apellidos'] . ' ' . $row_usuario['nombres']);
    } else {
        $usuario_nombre = 'Usuario desconocido';
    }

    // Obtener roles activos del usuario
    $sql_roles = "SELECT r.id, r.nombre 
                FROM roles_por_usuario ru
                INNER JOIN roles r ON ru.rol_id = r.id
                WHERE ru.usuario_id = $id_usuario 
                    AND ru.activo = 'S' AND ru.eliminado = 'N'
                    AND r.activo = 'S' AND r.eliminado = 'N'";
    $result_roles = $conexion->query($sql_roles);
    $roles_data = [];
    while ($row = $result_roles->fetch_assoc()) {
        $roles_usuario[] = $row['nombre']; // <-- Cambia aquí
        $roles_data[$row['nombre']] = $row['id'];
    }
    $conexion->close();
    
    // Determinar el rol actual y el menú correspondiente
    if (empty($roles_usuario)) { // <-- Cambia aquí
        $mensaje_sin_roles = 'No tienes roles asignados en el sistema. Contacta al administrador.';
        $sidebar_menu = [];
        $rol_id_actual = 0;
    } else {
        // Si hay un rol seleccionado en sesión, usarlo; si no, usar el primero
        if (isset($_SESSION['rol_actual']) && in_array($_SESSION['rol_actual'], $roles_usuario)) { // <-- Cambia aquí
            $rol_actual = $_SESSION['rol_actual'];
        } else {
            $rol_actual = $roles_usuario[0]; // <-- Cambia aquí
            $_SESSION['rol_actual'] = $rol_actual;
        }
        
        // Obtener el ID del rol actual
        $rol_id_actual = isset($roles_data[$rol_actual]) ? $roles_data[$rol_actual] : 0;
        $_SESSION['rol_id_actual'] = $rol_id_actual;
        
        // Obtener el menú correspondiente al rol actual
        $sidebar_menu = obtenerMenuPorRol($rol_actual);
    }
} else {
    // Usuario no logueado
    $usuario_nombre = 'Invitado';
    $roles_usuario = [];
    $sidebar_menu = [];
}

// Aquí, para no afectar el resto del sistema, crea una copia para el select y menú
$roles = $roles_usuario;
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo TITULO_DEL_SISTEMA; ?></title>
    
    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">

    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">



    <style>
        body {
            min-height: 100vh;
            position: relative;
            margin: 0;
            padding: 0;
        }
        .d-flex {
            position: relative;
            margin: 0;
            padding: 0;
        }
        .sidebar {
            min-height: 100vh;
            background: #dc3545;
            color: #fff;
            transition: width 0.3s;
            width: 250px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar.collapsed {
            width: 60px;
        }
        .sidebar .nav-link, .sidebar .dropdown-item {
            color: #fff;
        }
        .sidebar .nav-link.active, .sidebar .nav-link:hover {
            background: #bb2d3b;
            color: #fff;
        }
        .sidebar .sidebar-title {
            transition: opacity 0.3s;
            margin-left: 50px;
            position: relative;
        }
        .sidebar.collapsed .sidebar-title,
        .sidebar.collapsed .nav-link .sidebar-text {
            opacity: 0;
            pointer-events: none;
        }
        .sidebar .nav-link .sidebar-text {
            transition: opacity 0.3s;
        }
        .sidebar.collapsed .collapse {
            display: none !important;
        }
        .main-content {
            margin-left: 250px;
            transition: margin-left 0.3s;
            width: calc(100% - 250px);
            min-height: 100vh;
        }
        .sidebar.collapsed + .main-content {
            margin-left: 60px;
            width: calc(100% - 60px);
        }
        .navbar {
            background: #fff;
            border-bottom: 1px solid #dee2e6;
            margin: 0;
            padding: 0;
        }
        .navbar .form-select {
            min-width: 150px;
        }
        .hamburger {
            background: none;
            border: none;
            color: #fff;
            font-size: 2rem;
            z-index: 2000;
            transition: all 0.3s;
        }
        .hamburger:hover {
            color: #f8f9fa;
            transform: scale(1.1);
        }
        .hamburger.fixed-hamburger {
            position: absolute;
            left: 15px;
            top: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                z-index: 1040;
                left: 0;
                top: 0;
                height: 100vh;
                width: 250px;
                transform: translateX(-100%);
                transition: transform 0.3s;
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .sidebar.collapsed {
                width: 250px;
            }
            .main-content {
                margin-left: 0 !important;
                width: 100% !important;
            }
            .hamburger.fixed-hamburger {
                top: 15px;
                left: 15px;
                color: #dc3545;
                background: rgba(255, 255, 255, 0.9);
                border-radius: 5px;
                padding: 5px;
            }
            .sidebar .sidebar-title {
                margin-left: 50px;
            }
        }
        @media (min-width: 769px) {
            .hamburger.fixed-hamburger {
                left: 15px;
                top: 20px;
                color: #fff;
            }
            .sidebar .sidebar-title {
                margin-left: 50px;
            }
        }
    </style>

</head>
<body>

<div class="d-flex">

    <!-- Botón hamburguesa pegado al sidebar -->
    <button class="hamburger fixed-hamburger" id="toggleSidebar" aria-label="Menú">
        <i class="bi bi-list"></i>
    </button>

    <!-- Sidebar -->
    <nav id="sidebar" class="sidebar p-3">
        
        <h4 class="mb-4 sidebar-title">
    <a href="<?php echo DIRECCION_DEL_SITIO; ?>/backend/controladores/controlador_panel.php" class="text-white text-decoration-none">
        <?php echo TITULO_DEL_SISTEMA; ?>
    </a>
</h4>
        
        <?php if (!empty($mensaje_sin_roles)): ?>
            <div class="alert alert-warning text-center" role="alert">
                <i class="bi bi-exclamation-triangle"></i><br>
                <small><?php echo $mensaje_sin_roles; ?></small>
            </div>
        <?php elseif (!empty($sidebar_menu)): ?>
            <ul class="nav flex-column">
                <?php foreach ($sidebar_menu as $idx => $opcion): ?>
                    <li class="nav-item">
                        <a class="nav-link dropdown-toggle" data-bs-toggle="collapse" href="#submenu<?php echo $idx; ?>" role="button" aria-expanded="false">
                            <span class="sidebar-text"><?php echo htmlspecialchars($opcion['titulo']); ?></span>
                        </a>
                        <?php if (!empty($opcion['subopciones'])): ?>
                            <div class="collapse" id="submenu<?php echo $idx; ?>">
                                <ul class="nav flex-column ms-3">
                                    <?php foreach ($opcion['subopciones'] as $sub): ?>
                                        <li class="nav-item">
                                            <a class="nav-link" href="<?php echo htmlspecialchars($sub['enlace']); ?>">
                                                <span class="sidebar-text"><?php echo htmlspecialchars($sub['nombre']); ?></span>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php else: ?>
            <div class="text-center text-muted">
                <i class="bi bi-house"></i><br>
                <small>Menú no disponible</small>
            </div>
        <?php endif; ?>
    </nav>

    <!-- Área principal -->
    <div class="flex-grow-1 main-content">
        <!-- Header/Barra superior -->
        <nav class="navbar navbar-expand-lg navbar-light px-4">
            <div class="container-fluid">
                <!-- Select de roles y usuario en el centro -->
                <div class="mx-auto">
                    <div class="d-flex align-items-center gap-2">
                        <span class="fw-bold"><?php echo $usuario_nombre; ?></span>
                        <?php if (!empty($mensaje_sin_roles)): ?>
                            <span class="text-danger fw-bold ms-2"><?php echo $mensaje_sin_roles; ?></span>
                        <?php elseif (count($roles) > 1): ?>
                            <select class="form-select" id="selectRol" name="rol" style="width:auto;display:inline-block;">
                                <?php foreach($roles as $rol): ?>
                                    <option value="<?php echo htmlspecialchars($rol); ?>" <?php echo ($rol === $rol_actual) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($rol); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        <?php elseif (!empty($roles)): ?>
                            <span class="fw-bold ms-2"><?php echo htmlspecialchars($rol_actual); ?></span>
                        <?php endif; ?>
                    </div>
                </div>
                <!-- Menú usuario a la derecha -->
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-dark" href="#" id="usuarioDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-fill"></i> Usuario
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="usuarioDropdown">
                            <li><a class="dropdown-item" href="<?php echo DIRECCION_DEL_SITIO; ?>/backend/controladores/controlador_perfil.php">Perfil</a></li>
                            <li><a class="dropdown-item" href="<?php echo DIRECCION_DEL_SITIO; ?>/backend/controladores/salir.php">Salir</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </nav>
        <!-- Fin Header/Barra superior -->

        <!-- Inicio del contenido principal -->
        <main class="p-4">

<script>
    // Efecto hamburguesa para sidebar
    document.addEventListener('DOMContentLoaded', function() {
        const sidebar = document.getElementById('sidebar');
        const toggleBtn = document.getElementById('toggleSidebar');
        const selectRol = document.getElementById('selectRol');

        function toggleSidebar() {
            if (window.innerWidth < 769) {
                sidebar.classList.toggle('show');
            } else {
                sidebar.classList.toggle('collapsed');
            }
        }

        toggleBtn.addEventListener('click', toggleSidebar);

        // Cerrar sidebar al hacer clic fuera en móvil
        document.addEventListener('click', function(e) {
            if (window.innerWidth < 769 && sidebar.classList.contains('show')) {
                if (!sidebar.contains(e.target) && !toggleBtn.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });

        // Manejar cambio de rol
        if (selectRol) {
            selectRol.addEventListener('change', function() {
                const rolSeleccionado = this.value;
                
                // Enviar petición AJAX para cambiar el rol en sesión
                fetch('<?php echo DIRECCION_DEL_SITIO; ?>/backend/controladores/cambiar_rol.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'rol=' + encodeURIComponent(rolSeleccionado)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Recargar la página para actualizar el menú
                        window.location.reload();
                    } else {
                        alert('Error al cambiar el rol: ' + data.message);
                        // Revertir la selección
                        this.value = '<?php echo $rol_actual; ?>';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error de conexión al cambiar el rol');
                    // Revertir la selección
                    this.value = '<?php echo $rol_actual; ?>';
                });
            });
        }
    });
</script>