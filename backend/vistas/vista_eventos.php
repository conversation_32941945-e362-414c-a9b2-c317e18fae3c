<div class="container mt-4">
    <h3>Gestión de Eventos</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info"><?php echo htmlspecialchars($mensaje); ?></div>
    <?php endif; ?>

    <!-- Botón para abrir modal de nuevo evento -->
    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#modalEventoNuevo">
        <i class="bi bi-plus"></i> Nuevo Evento
    </button>

    <table id="tablaEventos" class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Evento</th>
                <th>Descripción</th>
                <th>Tipo de Evento</th>
                <th><PERSON>cha <PERSON></th>
                <th>Hora Desde</th>
                <th>Fecha Hasta</th>
                <th><PERSON>ra <PERSON></th>
                <th>Observaciones</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <th>ID</th>
                <th>Evento</th>
                <th>Descripción</th>
                <th>Tipo de Evento</th>
                <th>Fecha Desde</th>
                <th>Hora Desde</th>
                <th>Fecha Hasta</th>
                <th>Hora Hasta</th>
                <th>Observaciones</th>
                <th></th>
            </tr>
        </tfoot>
        <tbody>
        <?php
        if (isset($eventos_en_sistema) && is_array($eventos_en_sistema) && count($eventos_en_sistema) > 0) {
            foreach ($eventos_en_sistema as $evento) {
                if (is_array($evento)) {
                    $id = $evento['id'] ?? '';
                    $nombre_evento = $evento['evento'] ?? '';
                    $descripcion = $evento['descripcion'] ?? '';
                    $tipo_evento = $evento['tipo_evento'] ?? '';
                    $tipo_evento_nombre = $evento['tipo_evento_nombre'] ?? '';
                    $fecha_desde = $evento['fecha_desde'] ?? '';
                    $hora_desde = $evento['hora_desde'] ?? '';
                    $fecha_hasta = $evento['fecha_hasta'] ?? '';
                    $hora_hasta = $evento['hora_hasta'] ?? '';
                    $obs = $evento['obs'] ?? '';
                    
                    // Los campos ya vienen en formato correcto desde la BD
                    $fecha_desde_legible = $fecha_desde;
                    $hora_desde_legible = $hora_desde;
                    $fecha_hasta_legible = $fecha_hasta;
                    $hora_hasta_legible = $hora_hasta;
                    ?>
                    <tr>
                        <td><?php echo htmlspecialchars($id); ?></td>
                        <td><?php echo htmlspecialchars($nombre_evento); ?></td>
                        <td><?php echo htmlspecialchars(substr($descripcion, 0, 50)) . (strlen($descripcion) > 50 ? '...' : ''); ?></td>
                        <td><?php echo htmlspecialchars($tipo_evento_nombre); ?></td>
                        <td><?php echo htmlspecialchars($fecha_desde_legible); ?></td>
                        <td><?php echo htmlspecialchars($hora_desde_legible); ?></td>
                        <td><?php echo htmlspecialchars($fecha_hasta_legible); ?></td>
                        <td><?php echo htmlspecialchars($hora_hasta_legible); ?></td>
                        <td><?php echo htmlspecialchars(substr($obs, 0, 30)) . (strlen($obs) > 30 ? '...' : ''); ?></td>
                        <td>
                            <!-- Botón editar -->
                            <button class="btn btn-primary btn-sm btn-editar" 
                                data-id="<?php echo htmlspecialchars($id); ?>"
                                data-evento="<?php echo htmlspecialchars($nombre_evento); ?>"
                                data-descripcion="<?php echo htmlspecialchars($descripcion); ?>"
                                data-tipo_evento="<?php echo htmlspecialchars($tipo_evento); ?>"
                                data-fecha_desde="<?php echo htmlspecialchars($fecha_desde_legible); ?>"
                                data-hora_desde="<?php echo htmlspecialchars($hora_desde_legible); ?>"
                                data-fecha_hasta="<?php echo htmlspecialchars($fecha_hasta_legible); ?>"
                                data-hora_hasta="<?php echo htmlspecialchars($hora_hasta_legible); ?>"
                                data-obs="<?php echo htmlspecialchars($obs); ?>"
                                data-bs-toggle="modal" data-bs-target="#modalEventoEditar">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <!-- Botón eliminar -->
                            <form method="post" style="display:inline;">
                                <input type="hidden" name="id" value="<?php echo htmlspecialchars($id); ?>">
                                <input type="hidden" name="accion" value="eliminar">
                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Eliminar evento?');">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    <?php
                }
            }
        }
        ?>
        </tbody>
    </table>
</div>

<!-- Modal Nuevo Evento -->
<div class="modal fade" id="modalEventoNuevo" tabindex="-1" aria-labelledby="modalEventoNuevoLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <form method="post" class="modal-content" id="formNuevoEvento">
      <input type="hidden" name="accion" value="crear">
      <div class="modal-header">
        <h5 class="modal-title" id="modalEventoNuevoLabel">Nuevo Evento</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="row g-3">
            <div class="col-md-12">
                <label class="form-label">Nombre del Evento</label>
                <input type="text" id="evento_nuevo" name="evento" class="form-control" required>
            </div>
            <div class="col-md-12">
                <label class="form-label">Descripción</label>
                <textarea id="descripcion_nuevo" name="descripcion" class="form-control" rows="3"></textarea>
            </div>
            <div class="col-md-12">
                <label class="form-label">Tipo de Evento</label>
                <select id="tipo_evento_nuevo" name="tipo_evento" class="form-select">
                    <option value="">Seleccione un tipo de evento</option>
                    <?php foreach ($tipos_eventos_para_select as $tipo): ?>
                        <option value="<?php echo $tipo['id']; ?>">
                            <?php echo htmlspecialchars($tipo['nombre']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">Fecha Desde</label>
                <input type="date" id="fecha_desde_nuevo" name="fecha_desde" class="form-control">
            </div>
            <div class="col-md-6">
                <label class="form-label">Hora Desde</label>
                <input type="time" id="hora_desde_nuevo" name="hora_desde" class="form-control">
            </div>
            <div class="col-md-6">
                <label class="form-label">Fecha Hasta</label>
                <input type="date" id="fecha_hasta_nuevo" name="fecha_hasta" class="form-control">
            </div>
            <div class="col-md-6">
                <label class="form-label">Hora Hasta</label>
                <input type="time" id="hora_hasta_nuevo" name="hora_hasta" class="form-control">
            </div>
            <div class="col-md-12">
                <label class="form-label">Observaciones</label>
                <textarea id="obs_nuevo" name="obs" class="form-control" rows="3"></textarea>
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-success">Crear</button>
      </div>
    </form>
  </div>
</div>

<!-- Modal Editar Evento -->
<div class="modal fade" id="modalEventoEditar" tabindex="-1" aria-labelledby="modalEventoEditarLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <form method="post" class="modal-content" id="formEditarEvento">
      <input type="hidden" name="accion" value="editar">
      <input type="hidden" name="id" id="editar_id">
      <div class="modal-header">
        <h5 class="modal-title" id="modalEventoEditarLabel">Editar Evento</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="row g-3">
            <div class="col-md-12">
                <label class="form-label">Nombre del Evento</label>
                <input type="text" id="evento_editar" name="evento" class="form-control" required>
            </div>
            <div class="col-md-12">
                <label class="form-label">Descripción</label>
                <textarea id="descripcion_editar" name="descripcion" class="form-control" rows="3"></textarea>
            </div>
            <div class="col-md-12">
                <label class="form-label">Tipo de Evento</label>
                <select id="tipo_evento_editar" name="tipo_evento" class="form-select">
                    <option value="">Seleccione un tipo de evento</option>
                    <?php foreach ($tipos_eventos_para_select as $tipo): ?>
                        <option value="<?php echo $tipo['id']; ?>">
                            <?php echo htmlspecialchars($tipo['nombre']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">Fecha Desde</label>
                <input type="date" id="fecha_desde_editar" name="fecha_desde" class="form-control">
            </div>
            <div class="col-md-6">
                <label class="form-label">Hora Desde</label>
                <input type="time" id="hora_desde_editar" name="hora_desde" class="form-control">
            </div>
            <div class="col-md-6">
                <label class="form-label">Fecha Hasta</label>
                <input type="date" id="fecha_hasta_editar" name="fecha_hasta" class="form-control">
            </div>
            <div class="col-md-6">
                <label class="form-label">Hora Hasta</label>
                <input type="time" id="hora_hasta_editar" name="hora_hasta" class="form-control">
            </div>
            <div class="col-md-12">
                <label class="form-label">Observaciones</label>
                <textarea id="obs_editar" name="obs" class="form-control" rows="3"></textarea>
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary">Actualizar</button>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar DataTable
    var table = $('#tablaEventos').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
        },
        lengthMenu: [ [5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"] ],
        pageLength: 25, // Valor por defecto: 25 registros por página
        dom: 'lfrtip',
        scrollX: true, // Scroll horizontal para tabla ancha
        initComplete: function () {
            this.api().columns().every(function () {
                var column = this;
                if (column.index() === 9) return; // Excluir columna de acciones
                var input = document.createElement("input");
                input.className = "form-control form-control-sm";
                $(input).appendTo($(column.footer()).empty())
                .on('keyup change clear', function () {
                    if (column.search() !== this.value) {
                        column.search(this.value).draw();
                    }
                });
            });
        }
    });

    // Limpiar formulario de nuevo evento al abrir modal
    $('#modalEventoNuevo').on('show.bs.modal', function () {
        $('#formNuevoEvento')[0].reset();
    });

    // Rellenar modal de edición
    document.querySelectorAll('.btn-editar').forEach(btn => {
        btn.addEventListener('click', function() {
            document.getElementById('editar_id').value = this.dataset.id;
            document.getElementById('evento_editar').value = this.dataset.evento;
            document.getElementById('descripcion_editar').value = this.dataset.descripcion;
            document.getElementById('tipo_evento_editar').value = this.dataset.tipo_evento;
            document.getElementById('fecha_desde_editar').value = this.dataset.fecha_desde;
            document.getElementById('hora_desde_editar').value = this.dataset.hora_desde;
            document.getElementById('fecha_hasta_editar').value = this.dataset.fecha_hasta;
            document.getElementById('hora_hasta_editar').value = this.dataset.hora_hasta;
            document.getElementById('obs_editar').value = this.dataset.obs;
        });
    });
});
</script>
