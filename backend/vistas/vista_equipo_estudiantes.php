<div class="container mt-4">
    <h3>Gestión de Equipos - Estudiantes</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($mensaje); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#modalNuevaAsignacion">
        <i class="bi bi-plus"></i> Nueva Asignación
    </button>

    <table id="tablaEquipoEstudiantes" class="table table-striped table-bordered" style="width:100%">
        <thead>
            <tr>
                <th>ID</th>
                <th>Equipo</th>
                <th>Estudiante</th>
                <th>Carrera</th>
                <th>Entidad</th>
                <th>Observaciones</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <th>ID</th>
                <th>Equipo</th>
                <th>Estudiante</th>
                <th>Carrera</th>
                <th>Entidad</th>
                <th>Observaciones</th>
                <th></th>
            </tr>
        </tfoot>
        <tbody>
        <?php
        if (isset($asignaciones) && is_array($asignaciones) && count($asignaciones) > 0) {
            foreach ($asignaciones as $asignacion) {
                if (is_array($asignacion)) {
                    $id = $asignacion['id'] ?? '';
                    $equipo_id = $asignacion['equipo_id'] ?? '';
                    $estudiante_id = $asignacion['estudiante_id'] ?? '';
                    $equipo_nombre = $asignacion['equipo_nombre'] ?? '';
                    $estudiante_nombre = $asignacion['estudiante_nombre'] ?? '';
                    $carrera_nombre = $asignacion['carrera_nombre'] ?? '';
                    $entidad_nombre = $asignacion['entidad_nombre'] ?? '';
                    $obs = $asignacion['obs'] ?? '';
                    ?>
                    <tr>
                        <td><?php echo htmlspecialchars($id); ?></td>
                        <td><?php echo htmlspecialchars($equipo_nombre); ?></td>
                        <td><?php echo htmlspecialchars($estudiante_nombre); ?></td>
                        <td><?php echo htmlspecialchars($carrera_nombre); ?></td>
                        <td><?php echo htmlspecialchars($entidad_nombre); ?></td>
                        <td><?php echo htmlspecialchars($obs); ?></td>
                        <td>
                            <button class="btn btn-primary btn-sm btn-editar"
                                data-id="<?php echo htmlspecialchars($id); ?>"
                                data-equipo_id="<?php echo htmlspecialchars($equipo_id); ?>"
                                data-estudiante_id="<?php echo htmlspecialchars($estudiante_id); ?>"
                                data-obs="<?php echo htmlspecialchars($obs); ?>"
                                data-bs-toggle="modal" data-bs-target="#modalEditarAsignacion">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <form method="post" style="display:inline;" onsubmit="return confirm('¿Está seguro de eliminar esta asignación?');">
                                <input type="hidden" name="id" value="<?php echo htmlspecialchars($id); ?>">
                                <input type="hidden" name="accion" value="eliminar">
                                <button type="submit" class="btn btn-danger btn-sm">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    <?php
                }
            }
        }
        ?>
        </tbody>
    </table>
</div>

<!-- Modal Nueva Asignación -->
<div class="modal fade" id="modalNuevaAsignacion" tabindex="-1" aria-labelledby="modalNuevaAsignacionLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <form method="post" id="formNuevaAsignacion">
        <input type="hidden" name="accion" value="crear">
        <div class="modal-header">
          <h5 class="modal-title" id="modalNuevaAsignacionLabel">Nueva Asignación Equipo-Estudiante</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                  <label for="nuevo_equipo_id" class="form-label">Equipo <span class="text-danger">*</span></label>
                  <select name="equipo_id" id="nuevo_equipo_id" class="form-select" required>
                      <option value="">Seleccione un equipo...</option>
                      <?php if (isset($equipos_para_select) && is_array($equipos_para_select)): ?>
                          <?php foreach ($equipos_para_select as $equipo): ?>
                              <option value="<?php echo $equipo['id']; ?>">
                                  <?php echo htmlspecialchars($equipo['nombre']); ?>
                              </option>
                          <?php endforeach; ?>
                      <?php endif; ?>
                  </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                  <label for="nuevo_estudiante_id" class="form-label">Estudiante <span class="text-danger">*</span></label>
                  <select name="estudiante_id" id="nuevo_estudiante_id" class="form-select" required>
                      <option value="">Seleccione un estudiante...</option>
                      <?php if (isset($estudiantes_para_select) && is_array($estudiantes_para_select)): ?>
                          <?php foreach ($estudiantes_para_select as $estudiante): ?>
                              <option value="<?php echo $estudiante['id']; ?>"
                                      data-carrera="<?php echo htmlspecialchars($estudiante['carrera_nombre'] ?? ''); ?>"
                                      data-entidad="<?php echo htmlspecialchars($estudiante['entidad_nombre'] ?? ''); ?>">
                                  <?php echo htmlspecialchars($estudiante['nombre_completo']); ?>
                                  <?php if (!empty($estudiante['carrera_nombre'])): ?>
                                      - <?php echo htmlspecialchars($estudiante['carrera_nombre']); ?>
                                  <?php endif; ?>
                                  <?php if (!empty($estudiante['entidad_nombre'])): ?>
                                      (<?php echo htmlspecialchars($estudiante['entidad_nombre']); ?>)
                                  <?php endif; ?>
                              </option>
                          <?php endforeach; ?>
                      <?php endif; ?>
                  </select>
              </div>
            </div>
          </div>
          <div class="mb-3">
              <label for="nuevo_obs" class="form-label">Observaciones</label>
              <textarea name="obs" id="nuevo_obs" class="form-control" rows="3"></textarea>
          </div>
          <div id="info_estudiante_nuevo" class="alert alert-info" style="display: none;">
              <strong>Información del estudiante:</strong><br>
              <span id="info_carrera_nuevo"></span><br>
              <span id="info_entidad_nuevo"></span>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="submit" class="btn btn-success">
            <i class="bi bi-check"></i> Crear Asignación
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal Editar Asignación -->
<div class="modal fade" id="modalEditarAsignacion" tabindex="-1" aria-labelledby="modalEditarAsignacionLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <form method="post" id="formEditarAsignacion">
        <input type="hidden" name="accion" value="editar">
        <input type="hidden" name="id" id="editar_id">
        <div class="modal-header">
          <h5 class="modal-title" id="modalEditarAsignacionLabel">Editar Asignación Equipo-Estudiante</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                  <label for="editar_equipo_id" class="form-label">Equipo <span class="text-danger">*</span></label>
                  <select name="equipo_id" id="editar_equipo_id" class="form-select" required>
                      <option value="">Seleccione un equipo...</option>
                      <?php if (isset($equipos_para_select) && is_array($equipos_para_select)): ?>
                          <?php foreach ($equipos_para_select as $equipo): ?>
                              <option value="<?php echo $equipo['id']; ?>">
                                  <?php echo htmlspecialchars($equipo['nombre']); ?>
                              </option>
                          <?php endforeach; ?>
                      <?php endif; ?>
                  </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                  <label for="editar_estudiante_id" class="form-label">Estudiante <span class="text-danger">*</span></label>
                  <select name="estudiante_id" id="editar_estudiante_id" class="form-select" required>
                      <option value="">Seleccione un estudiante...</option>
                      <?php if (isset($estudiantes_para_select) && is_array($estudiantes_para_select)): ?>
                          <?php foreach ($estudiantes_para_select as $estudiante): ?>
                              <option value="<?php echo $estudiante['id']; ?>"
                                      data-carrera="<?php echo htmlspecialchars($estudiante['carrera_nombre'] ?? ''); ?>"
                                      data-entidad="<?php echo htmlspecialchars($estudiante['entidad_nombre'] ?? ''); ?>">
                                  <?php echo htmlspecialchars($estudiante['nombre_completo']); ?>
                                  <?php if (!empty($estudiante['carrera_nombre'])): ?>
                                      - <?php echo htmlspecialchars($estudiante['carrera_nombre']); ?>
                                  <?php endif; ?>
                                  <?php if (!empty($estudiante['entidad_nombre'])): ?>
                                      (<?php echo htmlspecialchars($estudiante['entidad_nombre']); ?>)
                                  <?php endif; ?>
                              </option>
                          <?php endforeach; ?>
                      <?php endif; ?>
                  </select>
              </div>
            </div>
          </div>
          <div class="mb-3">
              <label for="editar_obs" class="form-label">Observaciones</label>
              <textarea name="obs" id="editar_obs" class="form-control" rows="3"></textarea>
          </div>
          <div id="info_estudiante_editar" class="alert alert-info" style="display: none;">
              <strong>Información del estudiante:</strong><br>
              <span id="info_carrera_editar"></span><br>
              <span id="info_entidad_editar"></span>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="submit" class="btn btn-primary">
            <i class="bi bi-check"></i> Actualizar Asignación
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar DataTable
    var table = $('#tablaEquipoEstudiantes').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
        },
        lengthMenu: [ [5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"] ],
        pageLength: 25,
        dom: 'lfrtip',
        scrollX: true,
        order: [[0, 'desc']], // Ordenar por ID descendente
        initComplete: function () {
            // Agregar filtros en el footer
            this.api().columns().every(function () {
                var column = this;
                if (column.index() === 6) return; // Excluir columna de acciones
                var input = document.createElement("input");
                input.className = "form-control form-control-sm";
                input.placeholder = "Buscar...";
                $(input).appendTo($(column.footer()).empty())
                .on('keyup change clear', function () {
                    if (column.search() !== this.value) {
                        column.search(this.value).draw();
                    }
                });
            });
        }
    });

    // Función para mostrar información del estudiante
    function mostrarInfoEstudiante(selectElement, infoDiv, carreraSpan, entidadSpan) {
        var selectedOption = selectElement.options[selectElement.selectedIndex];
        if (selectedOption.value && selectedOption.dataset.carrera) {
            var carrera = selectedOption.dataset.carrera || 'No especificada';
            var entidad = selectedOption.dataset.entidad || 'No especificada';

            carreraSpan.textContent = 'Carrera: ' + carrera;
            entidadSpan.textContent = 'Entidad: ' + entidad;
            infoDiv.style.display = 'block';
        } else {
            infoDiv.style.display = 'none';
        }
    }

    // Eventos para mostrar información del estudiante en modal nuevo
    $('#nuevo_estudiante_id').on('change', function() {
        mostrarInfoEstudiante(
            this,
            document.getElementById('info_estudiante_nuevo'),
            document.getElementById('info_carrera_nuevo'),
            document.getElementById('info_entidad_nuevo')
        );
    });

    // Eventos para mostrar información del estudiante en modal editar
    $('#editar_estudiante_id').on('change', function() {
        mostrarInfoEstudiante(
            this,
            document.getElementById('info_estudiante_editar'),
            document.getElementById('info_carrera_editar'),
            document.getElementById('info_entidad_editar')
        );
    });

    // Limpiar formulario al abrir modal de nueva asignación
    $('#modalNuevaAsignacion').on('show.bs.modal', function () {
        $('#formNuevaAsignacion')[0].reset();
        $('#info_estudiante_nuevo').hide();
        $('#nuevo_equipo_id').focus();
    });

    // Manejar clic en botón editar
    $(document).on('click', '.btn-editar', function() {
        var id = $(this).data('id');
        var equipo_id = $(this).data('equipo_id');
        var estudiante_id = $(this).data('estudiante_id');
        var obs = $(this).data('obs');

        // Llenar el formulario de edición
        $('#editar_id').val(id);
        $('#editar_equipo_id').val(equipo_id);
        $('#editar_estudiante_id').val(estudiante_id);
        $('#editar_obs').val(obs);

        // Mostrar información del estudiante seleccionado
        mostrarInfoEstudiante(
            document.getElementById('editar_estudiante_id'),
            document.getElementById('info_estudiante_editar'),
            document.getElementById('info_carrera_editar'),
            document.getElementById('info_entidad_editar')
        );

        // Enfocar el campo equipo
        $('#modalEditarAsignacion').on('shown.bs.modal', function () {
            $('#editar_equipo_id').focus();
        });
    });

    // Validación de formularios
    $('#formNuevaAsignacion').on('submit', function(e) {
        var equipo_id = $('#nuevo_equipo_id').val();
        var estudiante_id = $('#nuevo_estudiante_id').val();

        if (!equipo_id) {
            e.preventDefault();
            alert('Debe seleccionar un equipo.');
            $('#nuevo_equipo_id').focus();
            return false;
        }

        if (!estudiante_id) {
            e.preventDefault();
            alert('Debe seleccionar un estudiante.');
            $('#nuevo_estudiante_id').focus();
            return false;
        }
    });

    $('#formEditarAsignacion').on('submit', function(e) {
        var equipo_id = $('#editar_equipo_id').val();
        var estudiante_id = $('#editar_estudiante_id').val();

        if (!equipo_id) {
            e.preventDefault();
            alert('Debe seleccionar un equipo.');
            $('#editar_equipo_id').focus();
            return false;
        }

        if (!estudiante_id) {
            e.preventDefault();
            alert('Debe seleccionar un estudiante.');
            $('#editar_estudiante_id').focus();
            return false;
        }
    });

    // Auto-cerrar alertas después de 5 segundos
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
});
</script>