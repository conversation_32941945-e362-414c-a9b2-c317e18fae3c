<?php
require_once __DIR__ . '/../controladores/controlador_usuario.php';
?>
<div class="container mt-4">
    <h3>Gestión de Usuarios</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info"><?php echo htmlspecialchars($mensaje); ?></div>
    <?php endif; ?>

    <!-- Botón para abrir modal de nuevo usuario -->
    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#modalUsuarioNuevo">
        <i class="bi bi-plus"></i> Nuevo Usuario
    </button>

    <table id="tablaUsuarios" class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Apellidos</th>
                <th>Nombres</th>
                <th>Email Institucional</th>
                <th>Email Alterno</th>
                <th>Sexo</th>
                <th>Estado Civil</th>
                <th>Teléfono</th>
                <th>Celular</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <th>ID</th>
                <th>Apellidos</th>
                <th>Nombres</th>
                <th>Email Institucional</th>
                <th>Email Alterno</th>
                <th>Sexo</th>
                <th>Estado Civil</th>
                <th>Teléfono</th>
                <th>Celular</th>
                <th></th>
            </tr>
        </tfoot>
        <tbody>
        <?php foreach ($usuarios as $u): ?>
            <tr>
                <td><?php echo $u['id']; ?></td>
                <td><?php echo $u['apellidos'] !== null ? htmlspecialchars($u['apellidos']) : ''; ?></td>
                <td><?php echo $u['nombres'] !== null ? htmlspecialchars($u['nombres']) : ''; ?></td>
                <td><?php echo $u['email_institucional'] !== null ? htmlspecialchars($u['email_institucional']) : ''; ?></td>
                <td><?php echo $u['email_alterno'] !== null ? htmlspecialchars($u['email_alterno']) : ''; ?></td>
                <td><?php echo $u['sexo'] !== null ? htmlspecialchars($u['sexo']) : ''; ?></td>
                <td><?php echo $u['estado_civil'] !== null ? htmlspecialchars($u['estado_civil']) : ''; ?></td>
                <td><?php echo $u['telefono'] !== null ? htmlspecialchars($u['telefono']) : ''; ?></td>
                <td><?php echo $u['celular'] !== null ? htmlspecialchars($u['celular']) : ''; ?></td>
                <td>
                    <!-- Botón editar -->
                    <button class="btn btn-primary btn-sm btn-editar" 
                        data-id="<?php echo $u['id']; ?>"
                        data-apellidos="<?php echo htmlspecialchars($u['apellidos'] ?? ''); ?>"
                        data-nombres="<?php echo htmlspecialchars($u['nombres'] ?? ''); ?>"
                        data-email_institucional="<?php echo htmlspecialchars($u['email_institucional'] ?? ''); ?>"
                        data-email_alterno="<?php echo htmlspecialchars($u['email_alterno'] ?? ''); ?>"
                        data-sexo="<?php echo $u['sexo'] !== null ? htmlspecialchars($u['sexo']) : ''; ?>"
                        data-estado_civil="<?php echo $u['estado_civil'] !== null ? htmlspecialchars($u['estado_civil']) : ''; ?>"
                        data-telefono="<?php echo htmlspecialchars($u['telefono'] ?? ''); ?>"
                        data-celular="<?php echo htmlspecialchars($u['celular'] ?? ''); ?>"
                        data-bs-toggle="modal" data-bs-target="#modalUsuarioEditar">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <!-- Botón eliminar -->
                    <form method="post" style="display:inline;">
                        <input type="hidden" name="id" value="<?php echo $u['id']; ?>">
                        <input type="hidden" name="accion" value="eliminar">
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Eliminar usuario?');">
                            <i class="bi bi-trash"></i>
                        </button>
                    </form>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>

<!-- Modal Nuevo Usuario -->
<div class="modal fade" id="modalUsuarioNuevo" tabindex="-1" aria-labelledby="modalUsuarioNuevoLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formNuevoUsuario">
      <input type="hidden" name="accion" value="crear">
      <div class="modal-header">
        <h5 class="modal-title" id="modalUsuarioNuevoLabel">Nuevo Usuario</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">Apellidos</label>
                <input type="text" id="apellidos_nuevo" name="apellidos" class="form-control" required>
            </div>
            <div class="col-md-6">
                <label class="form-label">Nombres</label>
                <input type="text" id="nombres_nuevo" name="nombres" class="form-control" required>
            </div>
            <div class="col-md-6">
                <label class="form-label">Email institucional</label>
                <input type="email" id="email_institucional_nuevo" name="email_institucional" class="form-control" required>
            </div>
            <div class="col-md-6">
                <label class="form-label">Email alterno</label>
                <input type="email" id="email_alterno_nuevo" name="email_alterno" class="form-control">
            </div>
            <div class="col-md-3">
                <label class="form-label">Sexo</label>
                <select id="sexo_nuevo" name="sexo" class="form-select">
                    <option value="">Seleccione</option>
                    <option value="M">Masculino</option>
                    <option value="F">Femenino</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Estado civil</label>
                <select id="estado_civil_nuevo" name="estado_civil" class="form-select">
                    <option value="">Seleccione</option>
                    <option value="Soltero(a)">Soltero(a)</option>
                    <option value="Casado(a)">Casado(a)</option>
                    <option value="Viudo(a)">Viudo(a)</option>
                    <option value="Divorciado(a)">Divorciado(a)</option>
                    <option value="UnionLibre">Unión Libre</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Teléfono</label>
                <input type="text" id="telefono_nuevo" name="telefono" class="form-control">
            </div>
            <div class="col-md-3">
                <label class="form-label">Celular</label>
                <input type="text" id="celular_nuevo" name="celular" class="form-control">
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-success">Crear</button>
      </div>
    </form>
  </div>
</div>

<!-- Modal Editar Usuario -->
<div class="modal fade" id="modalUsuarioEditar" tabindex="-1" aria-labelledby="modalUsuarioEditarLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formEditarUsuario">
      <input type="hidden" name="accion" value="editar">
      <input type="hidden" name="id" id="editar_id">
      <div class="modal-header">
        <h5 class="modal-title" id="modalUsuarioEditarLabel">Editar Usuario</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="row g-3">
            <div class="col-md-6">
                <label class="form-label">Apellidos</label>
                <input type="text" id="apellidos_editar" name="apellidos" class="form-control" required>
            </div>
            <div class="col-md-6">
                <label class="form-label">Nombres</label>
                <input type="text" id="nombres_editar" name="nombres" class="form-control" required>
            </div>
            <div class="col-md-6">
                <label class="form-label">Email institucional</label>
                <input type="email" id="email_institucional_editar" name="email_institucional" class="form-control" required>
            </div>
            <div class="col-md-6">
                <label class="form-label">Email alterno</label>
                <input type="email" id="email_alterno_editar" name="email_alterno" class="form-control">
            </div>
            <div class="col-md-3">
                <label class="form-label">Sexo</label>
                <select id="sexo_editar" name="sexo" class="form-select">
                    <option value="">Seleccione</option>
                    <option value="M">Masculino</option>
                    <option value="F">Femenino</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Estado civil</label>
                <select id="estado_civil_editar" name="estado_civil" class="form-select">
                    <option value="">Seleccione</option>
                    <option value="Soltero(a)">Soltero(a)</option>
                    <option value="Casado(a)">Casado(a)</option>
                    <option value="Viudo(a)">Viudo(a)</option>
                    <option value="Divorciado(a)">Divorciado(a)</option>
                    <option value="UnionLibre">Unión Libre</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Teléfono</label>
                <input type="text" id="telefono_editar" name="telefono" class="form-control">
            </div>
            <div class="col-md-3">
                <label class="form-label">Celular</label>
                <input type="text" id="celular_editar" name="celular" class="form-control">
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary">Actualizar</button>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar DataTable
    var table = $('#tablaUsuarios').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
        },
        lengthMenu: [ [5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"] ],
        pageLength: 25, // Valor por defecto: 25 registros por página
        dom: 'lfrtip',
        initComplete: function () {
            this.api().columns().every(function () {
                var column = this;
                if (column.index() === 9) return;
                var input = document.createElement("input");
                input.className = "form-control form-control-sm";
                $(input).appendTo($(column.footer()).empty())
                .on('keyup change clear', function () {
                    if (column.search() !== this.value) {
                        column.search(this.value).draw();
                    }
                });
            });
        }
    });

    // Limpiar formulario de nuevo usuario al abrir modal
    $('#modalUsuarioNuevo').on('show.bs.modal', function () {
        $('#formNuevoUsuario')[0].reset();
    });

    // Rellenar modal de edición
    document.querySelectorAll('.btn-editar').forEach(btn => {
        btn.addEventListener('click', function() {
            document.getElementById('editar_id').value = this.dataset.id;
            document.getElementById('apellidos_editar').value = this.dataset.apellidos;
            document.getElementById('nombres_editar').value = this.dataset.nombres;
            document.getElementById('email_institucional_editar').value = this.dataset.email_institucional;
            document.getElementById('email_alterno_editar').value = this.dataset.email_alterno;
            document.getElementById('sexo_editar').value = this.dataset.sexo;
            document.getElementById('estado_civil_editar').value = this.dataset.estado_civil;
            document.getElementById('telefono_editar').value = this.dataset.telefono;
            document.getElementById('celular_editar').value = this.dataset.celular;
        });
    });
});
</script>