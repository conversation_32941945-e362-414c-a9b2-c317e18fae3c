<!-- ... resto del HTML ... -->

<!-- jQ<PERSON>y y Select2 al final del body para evitar conflictos -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<div class="container mt-4">
    <h3>Gestión de Estudiantes</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info"><?php echo htmlspecialchars($mensaje); ?></div>
    <?php endif; ?>

    <!-- Botón para abrir modal de nuevo estudiante -->
    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#modalNuevoEstudiante">
        <i class="bi bi-plus"></i> Nuevo Estudiante
    </button>

    <table id="tablaEstudiantes" class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Usuario</th>
                <th>Carrera</th>
                <th>Obs</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <th>ID</th>
                <th>Usuario</th>
                <th>Carrera</th>
                <th>Obs</th>
                <th></th>
            </tr>
        </tfoot>
        <tbody>
        <?php foreach ($estudiantes as $e): ?>
            <tr>
                <td><?php echo $e['id']; ?></td>
                <td><?php echo htmlspecialchars(($e['usuario_apellidos'] ?? '') . ' ' . ($e['usuario_nombres'] ?? '')); ?></td>
                <td><?php echo htmlspecialchars($e['carrera_nombre'] ?? ''); ?><?php if (!empty($e['entidad_nombre'])): ?> (<?php echo htmlspecialchars($e['entidad_nombre']); ?>)<?php endif; ?></td>
                <td><?php echo htmlspecialchars($e['obs'] ?? ''); ?></td>
                <td>
                    <!-- Botón editar -->
                    <button class="btn btn-primary btn-sm btn-editar"
                        data-id="<?php echo $e['id']; ?>"
                        data-usuario_id="<?php echo $e['usuario_id']; ?>"
                        data-carrera_id="<?php echo $e['carrera_id']; ?>"
                        data-obs="<?php echo htmlspecialchars($e['obs'] ?? ''); ?>"
                        data-bs-toggle="modal" data-bs-target="#modalEditarEstudiante">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <!-- Botón eliminar -->
                    <form method="post" style="display:inline;">
                        <input type="hidden" name="id" value="<?php echo $e['id']; ?>">
                        <input type="hidden" name="accion" value="eliminar">
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Eliminar estudiante?');">
                            <i class="bi bi-trash"></i>
                        </button>
                    </form>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>

<!-- Modal Nuevo Estudiante -->
<div class="modal fade" id="modalNuevoEstudiante" tabindex="-1" aria-labelledby="modalNuevoEstudianteLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formNuevoEstudiante">
      <input type="hidden" name="accion" value="crear">
      <div class="modal-header">
        <h5 class="modal-title" id="modalNuevoEstudianteLabel">Nuevo Estudiante</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
            <label class="form-label">Usuario</label>
            <select name="usuario_id" class="form-select" required>
                <option value="">Seleccione</option>
                <?php foreach ($usuarios as $u): ?>
                    <option value="<?php echo $u['id']; ?>"><?php echo htmlspecialchars($u['apellidos'] . ' ' . $u['nombres']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Carrera</label>
            <select name="carrera_id" class="form-select" required>
                <option value="">Seleccione</option>
                <?php foreach ($carreras as $c): ?>
                    <option value="<?php echo $c['id']; ?>"><?php echo htmlspecialchars($c['nombre']); ?><?php if (!empty($c['entidad_nombre'])): ?> (Entidad: <?php echo htmlspecialchars($c['entidad_nombre']); ?>)<?php endif; ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Observaciones</label>
            <textarea name="obs" class="form-control"></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-success">Crear</button>
      </div>
    </form>
  </div>
</div>

<!-- Modal Editar Estudiante -->
<div class="modal fade" id="modalEditarEstudiante" tabindex="-1" aria-labelledby="modalEditarEstudianteLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formEditarEstudiante">
      <input type="hidden" name="accion" value="editar">
      <input type="hidden" name="id" id="editar_id">
      <div class="modal-header">
        <h5 class="modal-title" id="modalEditarEstudianteLabel">Editar Estudiante</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
            <label class="form-label">Usuario</label>
            <select name="usuario_id" class="form-select" id="editar_usuario_id" required>
                <option value="">Seleccione</option>
                <?php foreach ($usuarios as $u): ?>
                    <option value="<?php echo $u['id']; ?>"><?php echo htmlspecialchars($u['apellidos'] . ' ' . $u['nombres']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Carrera</label>
            <select name="carrera_id" class="form-select" id="editar_carrera_id" required>
                <option value="">Seleccione</option>
                <?php foreach ($carreras as $c): ?>
                    <option value="<?php echo $c['id']; ?>"><?php echo htmlspecialchars($c['nombre']); ?><?php if (!empty($c['entidad_nombre'])): ?> (<?php echo htmlspecialchars($c['entidad_nombre']); ?>)<?php endif; ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Observaciones</label>
            <textarea name="obs" class="form-control" id="editar_obs"></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary">Actualizar</button>
      </div>
    </form>
  </div>
</div>

<script>
$(document).ready(function() {
    // Inicializar DataTable
    var table = $('#tablaEstudiantes').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
        },
        lengthMenu: [ [5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"] ],
        pageLength: 25,
        dom: 'lfrtip',
        initComplete: function () {
            this.api().columns().every(function () {
                var column = this;
                if (column.index() === 4) return;
                var input = document.createElement("input");
                input.className = "form-control form-control-sm";
                $(input).appendTo($(column.footer()).empty())
                .on('keyup change clear', function () {
                    if (column.search() !== this.value) {
                        column.search(this.value).draw();
                    }
                });
            });
        }
    });

    
    // Limpiar formulario de nuevo estudiante al abrir modal
    $('#modalNuevoEstudiante').on('show.bs.modal', function () {
        $('#formNuevoEstudiante')[0].reset();
        $(this).find('.select2-usuario').val('').trigger('change');
        $(this).find('.select2-carrera').val('').trigger('change');
    });

    // Rellenar modal de edición
    document.querySelectorAll('.btn-editar').forEach(btn => {
        btn.addEventListener('click', function() {
            document.getElementById('editar_id').value = this.dataset.id;
            $('#editar_usuario_id').val(this.dataset.usuario_id).trigger('change');
            $('#editar_carrera_id').val(this.dataset.carrera_id).trigger('change');
            document.getElementById('editar_obs').value = this.dataset.obs;
        });
    });
});
</script>
