
<?php
// Obtener el rol_id actual de la sesión
$rol_id_actual = isset($_SESSION['rol_id_actual']) ? $_SESSION['rol_id_actual'] : 0;
$rol_actual = isset($_SESSION['rol_actual']) ? $_SESSION['rol_actual'] : 'Usuario';

// Mostrar contenido según el rol_id
if ($rol_id_actual == 1): // Admin (rol_id = 1)
?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="bi bi-shield-check"></i> <strong>Panel de Administrador</strong> - Tienes acceso completo al sistema
            </div>
        </div>
    </div>

    <div class="accordion" id="accordionAdmin">
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseAgenda" aria-expanded="true" aria-controls="collapseAgenda">
                    <i class="bi bi-calendar3 me-2"></i> Agenda General
                </button>
            </h2>
            <div id="collapseAgenda" class="accordion-collapse collapse show" data-bs-parent="#accordionAdmin">
                
                <div class="accordion-body">
                    <strong>Agenda General</strong> - Gestión de eventos y tareas.

                    <?php
                    // --- Conexión a la base de datos (ajusta los datos si es necesario) ---
                    $mysqli = new mysqli(SERVIDOR, USUARIO, CLAVE, BD_SISTEMA);
                    if ($mysqli->connect_errno) {
                        echo '<div class="alert alert-danger">Error de conexión a la base de datos.</div>';
                        return;
                    }

                    // --- Obtener tipos de eventos activos ---
                    $tipos_eventos = [];
                    $res = $mysqli->query("SELECT id, nombre FROM tipos_eventos WHERE activo='S' AND eliminado='N' ORDER BY nombre");
                    if ($res) {
                        while ($row = $res->fetch_assoc()) {
                            $tipos_eventos[] = $row;
                        }
                        $res->free();
                    }

                    // --- Procesar filtros del formulario ---
                    $condiciones = ["e.activo='S'", "e.eliminado='N'"];
                    $params = [];
                    if (!empty($_GET['tipo_evento'])) {
                        $condiciones[] = "e.tipo_evento = ?";
                        $params[] = $_GET['tipo_evento'];
                    }
                    if (!empty($_GET['fecha_desde'])) {
                        $condiciones[] = "e.fecha_desde >= ?";
                        $params[] = $_GET['fecha_desde'];
                    }
                    if (!empty($_GET['fecha_hasta'])) {
                        $condiciones[] = "e.fecha_hasta <= ?";
                        $params[] = $_GET['fecha_hasta'];
                    }
                    $where = implode(' AND ', $condiciones);

                    // --- Consulta de eventos filtrados ---
                    $eventos_filtrados = [];
                    $sql = "SELECT e.*, t.nombre AS tipo_evento_nombre
                            FROM eventos e
                            LEFT JOIN tipos_eventos t ON e.tipo_evento = t.id
                            WHERE $where
                            ORDER BY e.fecha_desde DESC, e.fecha_desde_hora_inicio ASC";
                    $stmt = $mysqli->prepare($sql);

                    // Vincular parámetros si hay filtros
                    if (count($params) > 0) {
                        $types = str_repeat('s', count($params));
                        $stmt->bind_param($types, ...$params);
                    }
                    $stmt->execute();
                    $result = $stmt->get_result();
                    if ($result) {
                        while ($row = $result->fetch_assoc()) {
                            $eventos_filtrados[] = $row;
                        }
                        $result->free();
                    }
                    $stmt->close();
                    $mysqli->close();
                    ?>

                    <form method="get" action="panel" class="row g-2 align-items-end mb-3">

                        <div class="col-md-4">
                            <label for="tipo_evento" class="form-label mb-0">Tipo de Evento</label>
                            <select name="tipo_evento" id="tipo_evento" class="form-select">
                                <option value="">-- Todos --</option>
                                <?php foreach ($tipos_eventos as $tipo): ?>
                                    <option value="<?= $tipo['id'] ?>" <?= (isset($_GET['tipo_evento']) && $_GET['tipo_evento'] == $tipo['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($tipo['nombre']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="fecha_desde" class="form-label mb-0">Desde</label>
                            <input type="date" name="fecha_desde" id="fecha_desde" class="form-control" value="<?= htmlspecialchars($_GET['fecha_desde'] ?? '') ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="fecha_hasta" class="form-label mb-0">Hasta</label>
                            <input type="date" name="fecha_hasta" id="fecha_hasta" class="form-control" value="<?= htmlspecialchars($_GET['fecha_hasta'] ?? '') ?>">
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">Buscar</button>
                        </div>
                    </form>

                    <?php if (count($eventos_filtrados) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead>
                                    <tr>
                                        <th>Evento</th>
                                        <th>Descripción</th>
                                        <th>Tipo</th>
                                        <th>Fecha Desde</th>
                                        <th>Hora Inicio Desde</th>
                                        <th>Hora Final Desde</th>
                                        <th>Fecha Hasta</th>
                                        <th>Hora Inicio Hasta</th>
                                        <th>Hora Final Hasta</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php foreach ($eventos_filtrados as $ev): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($ev['evento']) ?></td>
                                        <td><?= htmlspecialchars($ev['descripcion']) ?></td>
                                        <td><?= htmlspecialchars($ev['tipo_evento_nombre']) ?></td>
                                        <td><?= htmlspecialchars($ev['fecha_desde']) ?></td>
                                        <td><?= htmlspecialchars($ev['fecha_desde_hora_inicio']) ?></td>
                                        <td><?= htmlspecialchars($ev['fecha_desde_hora_final']) ?></td>
                                        <td><?= htmlspecialchars($ev['fecha_hasta']) ?></td>
                                        <td><?= htmlspecialchars($ev['fecha_hasta_hora_inicio']) ?></td>
                                        <td><?= htmlspecialchars($ev['fecha_hasta_hora_final']) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php elseif ($_GET): ?>
                        <div class="alert alert-warning">No se encontraron eventos para los filtros seleccionados.</div>
                    <?php endif; ?>
                </div>
                
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseContadores" aria-expanded="false" aria-controls="collapseContadores">
                    <i class="bi bi-graph-up me-2"></i> Contadores del Sistema
                </button>
            </h2>
            <div id="collapseContadores" class="accordion-collapse collapse" data-bs-parent="#accordionAdmin">
                <div class="accordion-body">
                    <strong>Contadores del Sistema</strong> - Estadísticas completas y métricas de rendimiento.
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">1,234</h5>
                                    <p class="card-text">Total Usuarios</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-success">567</h5>
                                    <p class="card-text">Usuarios Activos</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-warning">89</h5>
                                    <p class="card-text">Carreras Participantes</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-info">12</h5>
                                    <p class="card-text">Estudiantes Registrados</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseGraficas" aria-expanded="false" aria-controls="collapseGraficas">
                    <i class="bi bi-bar-chart me-2"></i> Gráficas Avanzadas
                </button>
            </h2>
            <div id="collapseGraficas" class="accordion-collapse collapse" data-bs-parent="#accordionAdmin">
                <div class="accordion-body">
                    <!-- Gráficas Avanzadas -->
                    <strong>Gráficas Avanzadas</strong> - Análisis detallado y visualización de datos.
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div id="graficoPastel" style="height: 300px;"></div>
                        </div>
                        <div class="col-md-6">
                            <div id="graficoBarras" style="height: 300px;"></div>
                        </div>
                    </div>
                    

                    <!-- Google Charts Loader -->
                    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
                    <script>
                    // Cargar Google Charts
                    google.charts.load('current', {'packages':['corechart']});
                    google.charts.setOnLoadCallback(drawCharts);

                    function drawCharts() {
                        // Datos simulados para pastel: distribución de roles en la tabla usuarios
                        var dataPastel = google.visualization.arrayToDataTable([
                            ['Rol', 'Cantidad'],
                            ['Admin', 1],
                            ['Coordinador General', 2],
                            ['Docente Carrera', 3],
                            ['Estudiante Carrera', 5],
                            ['Juez Competencia', 1]
                        ]);

                        var optionsPastel = {
                            title: 'Distribución de Roles',
                            pieHole: 0.4,
                            legend: { position: 'right' }
                        };

                        var chartPastel = new google.visualization.PieChart(document.getElementById('graficoPastel'));
                        chartPastel.draw(dataPastel, optionsPastel);

                        // Datos simulados para barras: operaciones CRUD en auditoria_crud
                        var dataBarras = google.visualization.arrayToDataTable([
                            ['Competencia', 'Cantidad', { role: 'style' }],
                            ['C1', 12, '#28a745'],
                            ['C2', 30, '#007bff'],
                            ['C3', 7, '#ffc107'],
                            ['C4', 2, '#dc3545'],
                            ['C5', 22, '#dc3570']
                        ]);

                        var optionsBarras = {
                            title: 'Preguntas por Competencia',
                            legend: { position: 'none' },
                            bars: 'vertical',
                            vAxis: { minValue: 0 }
                        };

                        var chartBarras = new google.visualization.ColumnChart(document.getElementById('graficoBarras'));
                        chartBarras.draw(dataBarras, optionsBarras);
                    }
                    </script>
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDetalles" aria-expanded="false" aria-controls="collapseDetalles">
                    <i class="bi bi-gear me-2"></i> Otras Necesidades
                </button>
            </h2>
            <div id="collapseDetalles" class="accordion-collapse collapse" data-bs-parent="#accordionAdmin">
                <div class="accordion-body">
                    <strong>En relación a las Competencias</strong> - Variedad.
                    <div class="row mt-3">
                        <div class="col-md-6 mb-4">
                            <div id="graficoCarreras" style="height: 250px;"></div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div id="graficoCompetenciasCarrera" style="height: 250px;"></div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div id="graficoEquiposCompetencia" style="height: 250px;"></div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div id="graficoEstudiantesEquipo" style="height: 250px;"></div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info mt-2">
                                <i class="bi bi-info-circle"></i> <b>Nota:</b> Los datos son simulados. Integra tus consultas MySQL para mostrar datos reales.
                            </div>
                        </div>
                    </div>

                    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
                    <script>
                        google.charts.load('current', {'packages':['corechart', 'bar']});
                        google.charts.setOnLoadCallback(drawAllCharts);

                        function drawAllCharts() {
                            // 1. Carreras (Pastel)
                            var dataCarreras = google.visualization.arrayToDataTable([
                                ['Carrera', 'Cantidad'],
                                ['Ingeniería', 4],
                                ['Educación', 3],
                                ['Derecho', 2],
                                ['Administración', 5]
                            ]);
                            var optCarreras = { title: 'Carreras', pieHole: 0, legend: { position: 'right' } };
                            new google.visualization.PieChart(document.getElementById('graficoCarreras')).draw(dataCarreras, optCarreras);

                            // 2. Competencias por Carrera (Barras)
                            var dataCompCarrera = google.visualization.arrayToDataTable([
                                ['Carrera', 'Competencias'],
                                ['Ingeniería', 6],
                                ['Educación', 4],
                                ['Derecho', 3],
                                ['Administración', 5]
                            ]);
                            var optCompCarrera = { title: 'Competencias por Carrera', legend: { position: 'none' }, bars: 'horizontal', colors: ['#007bff'] };
                            new google.charts.Bar(document.getElementById('graficoCompetenciasCarrera')).draw(dataCompCarrera, google.charts.Bar.convertOptions(optCompCarrera));

                            // 3. Equipos por Competencia (Columnas)
                            var dataEquiposComp = google.visualization.arrayToDataTable([
                                ['Competencia', 'Equipos'],
                                ['Comp. 1', 3],
                                ['Comp. 2', 5],
                                ['Comp. 3', 2],
                                ['Comp. 4', 4]
                            ]);
                            var optEquiposComp = { title: 'Equipos por Competencia', legend: { position: 'none' }, colors: ['#28a745'] };
                            new google.visualization.ColumnChart(document.getElementById('graficoEquiposCompetencia')).draw(dataEquiposComp, optEquiposComp);

                            // 4. Estudiantes por Equipo (Donut)
                            var dataEstudEquipo = google.visualization.arrayToDataTable([
                                ['Equipo', 'Estudiantes'],
                                ['Equipo A', 6],
                                ['Equipo B', 4],
                                ['Equipo C', 5],
                                ['Equipo D', 3]
                            ]);
                            var optEstudEquipo = { title: 'Estudiantes por Equipo', pieHole: 0.5, legend: { position: 'right' } };
                            new google.visualization.PieChart(document.getElementById('graficoEstudiantesEquipo')).draw(dataEstudEquipo, optEstudEquipo);
                        }
                        </script>
                    </div>
                </div>
            </div>
        </div>

<?php elseif ($rol_id_actual == 2): // Usuario normal (rol_id = 2) ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-primary">
                <i class="bi bi-person"></i> <strong>Panel de Usuario</strong> - Bienvenido a tu espacio personal
            </div>
        </div>
    </div>

    <div class="accordion" id="accordionUser">
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMiAgenda" aria-expanded="true" aria-controls="collapseMiAgenda">
                    <i class="bi bi-calendar-event me-2"></i> Mi Agenda Personal
                </button>
            </h2>
            <div id="collapseMiAgenda" class="accordion-collapse collapse show" data-bs-parent="#accordionUser">
                <div class="accordion-body">
                    <strong>Mi Agenda Personal</strong> - Gestiona tus eventos y recordatorios.
                    <div class="mt-3">
                        <p>Próximos eventos:</p>
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Reunión de equipo</strong><br>
                                    <small class="text-muted">Sala de conferencias A</small>
                                </div>
                                <span class="badge bg-primary rounded-pill">Hoy 14:00</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Entrega de proyecto</strong><br>
                                    <small class="text-muted">Proyecto final del módulo</small>
                                </div>
                                <span class="badge bg-warning rounded-pill">Mañana</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Capacitación</strong><br>
                                    <small class="text-muted">Nuevas herramientas</small>
                                </div>
                                <span class="badge bg-info rounded-pill">Viernes</span>
                            </li>
                        </ul>
                        <div class="mt-3">
                            <button class="btn btn-primary btn-sm me-2">Agregar Evento</button>
                            <button class="btn btn-outline-primary btn-sm">Ver Calendario Completo</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMiActividad" aria-expanded="false" aria-controls="collapseMiActividad">
                    <i class="bi bi-activity me-2"></i> Mi Actividad
                </button>
            </h2>
            <div id="collapseMiActividad" class="accordion-collapse collapse" data-bs-parent="#accordionUser">
                <div class="accordion-body">
                    <strong>Mi Actividad</strong> - Resumen de tu actividad reciente.
                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h5 class="card-title text-success">15</h5>
                                        <p class="card-text">Tareas Completadas</p>
                                        <small class="text-muted">Esta semana</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h5 class="card-title text-warning">3</h5>
                                        <p class="card-text">Tareas Pendientes</p>
                                        <small class="text-muted">Por completar</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <h6>Actividad Reciente:</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success"></i> Completaste "Revisión de documentos" - Hace 2 horas</li>
                                <li><i class="bi bi-clock text-warning"></i> Iniciaste "Análisis de datos" - Hace 4 horas</li>
                                <li><i class="bi bi-check-circle text-success"></i> Completaste "Reunión con cliente" - Ayer</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseMiPerfil" aria-expanded="false" aria-controls="collapseMiPerfil">
                    <i class="bi bi-person-gear me-2"></i> Mi Perfil
                </button>
            </h2>
            <div id="collapseMiPerfil" class="accordion-collapse collapse" data-bs-parent="#accordionUser">
                <div class="accordion-body">
                    <strong>Mi Perfil</strong> - Información personal y configuración.
                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-8">
                                <h6>Información Personal</h6>
                                <p><strong>Último acceso:</strong> Hoy a las 09:30</p>
                                <p><strong>Sesiones activas:</strong> 1</p>
                                <p><strong>Rol actual:</strong> <?php echo htmlspecialchars($rol_actual); ?></p>
                            </div>
                            <div class="col-md-4">
                                <h6>Acciones</h6>
                                <button class="btn btn-outline-primary btn-sm d-block mb-2 w-100">Editar Perfil</button>
                                <button class="btn btn-outline-secondary btn-sm d-block w-100">Cambiar Contraseña</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php elseif ($rol_id_actual == 3): // Moderador (rol_id = 3) ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning">
                <i class="bi bi-shield-exclamation"></i> <strong>Panel de Moderador</strong> - Gestión de contenido y usuarios
            </div>
        </div>
    </div>

    <div class="accordion" id="accordionModerator">
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseModeracion" aria-expanded="true" aria-controls="collapseModeracion">
                    <i class="bi bi-shield-check me-2"></i> Panel de Moderación
                </button>
            </h2>
            <div id="collapseModeracion" class="accordion-collapse collapse show" data-bs-parent="#accordionModerator">
                <div class="accordion-body">
                    <strong>Panel de Moderación</strong> - Gestión de contenido y reportes.
                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-danger">5</h5>
                                        <p class="card-text">Reportes Pendientes</p>
                                        <button class="btn btn-outline-danger btn-sm">Revisar</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-warning">12</h5>
                                        <p class="card-text">Contenido en Revisión</p>
                                        <button class="btn btn-outline-warning btn-sm">Ver</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-success">28</h5>
                                        <p class="card-text">Usuarios Activos</p>
                                        <button class="btn btn-outline-success btn-sm">Gestionar</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseContenido" aria-expanded="false" aria-controls="collapseContenido">
                    <i class="bi bi-file-text me-2"></i> Gestión de Contenido
                </button>
            </h2>
            <div id="collapseContenido" class="accordion-collapse collapse" data-bs-parent="#accordionModerator">
                <div class="accordion-body">
                    <strong>Gestión de Contenido</strong> - Moderación y aprobación de contenido.
                    <div class="mt-3">
                        <h6>Contenido Reciente:</h6>
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Publicación de Juan Pérez</strong><br>
                                    <small class="text-muted">Hace 30 minutos</small>
                                </div>
                                <div>
                                    <button class="btn btn-success btn-sm me-1">Aprobar</button>
                                    <button class="btn btn-danger btn-sm">Rechazar</button>
                                </div>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Comentario de María García</strong><br>
                                    <small class="text-muted">Hace 1 hora</small>
                                </div>
                                <div>
                                    <button class="btn btn-success btn-sm me-1">Aprobar</button>
                                    <button class="btn btn-danger btn-sm">Rechazar</button>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary btn-sm me-2">Ver Todo el Contenido</button>
                            <button class="btn btn-outline-primary btn-sm">Configurar Filtros</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseUsuarios" aria-expanded="false" aria-controls="collapseUsuarios">
                    <i class="bi bi-people me-2"></i> Gestión de Usuarios
                </button>
            </h2>
            <div id="collapseUsuarios" class="accordion-collapse collapse" data-bs-parent="#accordionModerator">
                <div class="accordion-body">
                    <strong>Gestión de Usuarios</strong> - Supervisión y administración de usuarios.
                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Estadísticas de Usuarios</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-person-check text-success"></i> Usuarios activos: 28</li>
                                    <li><i class="bi bi-person-dash text-warning"></i> Usuarios suspendidos: 3</li>
                                    <li><i class="bi bi-person-plus text-info"></i> Nuevos registros hoy: 2</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Acciones Rápidas</h6>
                                <button class="btn btn-outline-info btn-sm d-block mb-2 w-100">Ver Lista Completa</button>
                                <button class="btn btn-outline-warning btn-sm d-block mb-2 w-100">Usuarios Reportados</button>
                                <button class="btn btn-outline-secondary btn-sm d-block w-100">Generar Reporte</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php else: // Sin rol o rol no reconocido ?>
    <div class="row">
        <div class="col-12">
            <div class="alert alert-danger text-center">
                <i class="bi bi-exclamation-triangle-fill"></i>
                <h4>Acceso Restringido</h4>
                <p>No tienes permisos para acceder a este panel o tu rol no está configurado correctamente.</p>
                <p><strong>Rol actual:</strong> <?php echo htmlspecialchars($rol_actual); ?> (ID: <?php echo $rol_id_actual; ?>)</p>
                <hr>
                <p class="mb-0">Contacta al administrador del sistema para obtener los permisos necesarios.</p>
                <div class="mt-3">
                    <button class="btn btn-outline-primary" onclick="window.location.reload()">Recargar Página</button>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Scripts adicionales para funcionalidad del panel -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Agregar funcionalidad a los botones de ejemplo
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        if (button.textContent.includes('Aprobar') || button.textContent.includes('Rechazar')) {
            button.addEventListener('click', function() {
                const action = this.textContent;
                const item = this.closest('.list-group-item');
                if (item) {
                    item.style.opacity = '0.5';
                    setTimeout(() => {
                        item.remove();
                    }, 500);
                }
                // Aquí podrías agregar una llamada AJAX real
                console.log(`Acción: ${action} ejecutada`);
            });
        }
    });
    
    // Funcionalidad para botones de estadísticas
    const statButtons = document.querySelectorAll('.card .btn');
    statButtons.forEach(button => {
        button.addEventListener('click', function() {
            const cardTitle = this.closest('.card').querySelector('.card-title').textContent;
            alert(`Funcionalidad para: ${this.textContent} - Valor: ${cardTitle}`);
        });
    });
});
</script>