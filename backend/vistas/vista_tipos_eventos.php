<div class="container mt-4">
    <h3>Gestión de Tipos de Eventos</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info"><?php echo htmlspecialchars($mensaje); ?></div>
    <?php endif; ?>

    <!-- Botón para abrir modal de nuevo tipo de evento -->
    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#modalTipoEventoNuevo">
        <i class="bi bi-plus"></i> Nuevo Tipo de Evento
    </button>

    <table id="tablaTiposEventos" class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Nombre</th>
                <th>Observaciones</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <th>ID</th>
                <th>Nombre</th>
                <th>Observaciones</th>
                <th></th>
            </tr>
        </tfoot>
        <tbody>
        <?php
        if (isset($tipos_eventos_en_sistema) && is_array($tipos_eventos_en_sistema) && count($tipos_eventos_en_sistema) > 0) {
            foreach ($tipos_eventos_en_sistema as $tipo_evento) {
                if (is_array($tipo_evento) && isset($tipo_evento['id']) && isset($tipo_evento['nombre'])) {
                    $id = $tipo_evento['id'] ?? '';
                    $nombre = $tipo_evento['nombre'] ?? '';
                    $obs = $tipo_evento['obs'] ?? '';
                    ?>
                    <tr>
                        <td><?php echo htmlspecialchars($id); ?></td>
                        <td><?php echo htmlspecialchars($nombre); ?></td>
                        <td><?php echo htmlspecialchars($obs); ?></td>
                        <td>
                            <!-- Botón editar -->
                            <button class="btn btn-primary btn-sm btn-editar" 
                                data-id="<?php echo htmlspecialchars($id); ?>"
                                data-nombre="<?php echo htmlspecialchars($nombre); ?>"
                                data-obs="<?php echo htmlspecialchars($obs); ?>"
                                data-bs-toggle="modal" data-bs-target="#modalTipoEventoEditar">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <!-- Botón eliminar -->
                            <form method="post" style="display:inline;">
                                <input type="hidden" name="id" value="<?php echo htmlspecialchars($id); ?>">
                                <input type="hidden" name="accion" value="eliminar">
                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Eliminar tipo de evento?');">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    <?php
                }
            }
        }
        ?>
        </tbody>
    </table>
</div>

<!-- Modal Nuevo Tipo de Evento -->
<div class="modal fade" id="modalTipoEventoNuevo" tabindex="-1" aria-labelledby="modalTipoEventoNuevoLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formNuevoTipoEvento">
      <input type="hidden" name="accion" value="crear">
      <div class="modal-header">
        <h5 class="modal-title" id="modalTipoEventoNuevoLabel">Nuevo Tipo de Evento</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="row g-3">
            <div class="col-md-12">
                <label class="form-label">Nombre del Tipo de Evento</label>
                <input type="text" id="nombre_nuevo" name="nombre" class="form-control" required>
            </div>
            <div class="col-md-12">
                <label class="form-label">Observaciones</label>
                <textarea id="obs_nuevo" name="obs" class="form-control" rows="3"></textarea>
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-success">Crear</button>
      </div>
    </form>
  </div>
</div>

<!-- Modal Editar Tipo de Evento -->
<div class="modal fade" id="modalTipoEventoEditar" tabindex="-1" aria-labelledby="modalTipoEventoEditarLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="post" class="modal-content" id="formEditarTipoEvento">
      <input type="hidden" name="accion" value="editar">
      <input type="hidden" name="id" id="editar_id">
      <div class="modal-header">
        <h5 class="modal-title" id="modalTipoEventoEditarLabel">Editar Tipo de Evento</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="row g-3">
            <div class="col-md-12">
                <label class="form-label">Nombre del Tipo de Evento</label>
                <input type="text" id="nombre_editar" name="nombre" class="form-control" required>
            </div>
            <div class="col-md-12">
                <label class="form-label">Observaciones</label>
                <textarea id="obs_editar" name="obs" class="form-control" rows="3"></textarea>
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary">Actualizar</button>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar DataTable
    var table = $('#tablaTiposEventos').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
        },
        lengthMenu: [ [5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"] ],
        pageLength: 25, // Valor por defecto: 25 registros por página
        dom: 'lfrtip',
        initComplete: function () {
            this.api().columns().every(function () {
                var column = this;
                if (column.index() === 3) return; // Excluir columna de acciones
                var input = document.createElement("input");
                input.className = "form-control form-control-sm";
                $(input).appendTo($(column.footer()).empty())
                .on('keyup change clear', function () {
                    if (column.search() !== this.value) {
                        column.search(this.value).draw();
                    }
                });
            });
        }
    });

    // Limpiar formulario de nuevo tipo de evento al abrir modal
    $('#modalTipoEventoNuevo').on('show.bs.modal', function () {
        $('#formNuevoTipoEvento')[0].reset();
    });

    // Rellenar modal de edición
    document.querySelectorAll('.btn-editar').forEach(btn => {
        btn.addEventListener('click', function() {
            document.getElementById('editar_id').value = this.dataset.id;
            document.getElementById('nombre_editar').value = this.dataset.nombre;
            document.getElementById('obs_editar').value = this.dataset.obs;
        });
    });
});
</script>
