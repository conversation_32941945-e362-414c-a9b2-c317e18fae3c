<div class="container mt-4">
    <h3>Gestión de Olimpiadas</h3>
    <?php if (!empty($mensaje)): ?>
        <div class="alert alert-info"><?php echo htmlspecialchars($mensaje); ?></div>
    <?php endif; ?>

    <!-- Botón para abrir modal de nueva olimpiada -->
    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#modalOlimpiadaNueva">
        <i class="bi bi-plus"></i> Nueva Olimpiada
    </button>

    <table id="tablaOlimpiadas" class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Nombre</th>
                <th>Interna</th>
                <th>Fecha Desde</th>
                <th><PERSON><PERSON></th>
                <th>Detalles</th>
                <th>Observaciones</th>
                <th>Acciones</th>
            </tr>
        </thead>
        <tfoot>
            <tr>
                <th>ID</th>
                <th>Nombre</th>
                <th>Interna</th>
                <th><PERSON><PERSON></th>
                <th><PERSON><PERSON></th>
                <th>Detalles</th>
                <th>Observaciones</th>
                <th></th>
            </tr>
        </tfoot>
        <tbody>
        <?php
        if (isset($olimpiadas_en_sistema) && is_array($olimpiadas_en_sistema) && count($olimpiadas_en_sistema) > 0) {
            foreach ($olimpiadas_en_sistema as $olimpiada) {
                if (is_array($olimpiada)) {
                    $id = $olimpiada['id'] ?? '';
                    $nombre = $olimpiada['nombre'] ?? '';
                    $Interna = $olimpiada['Interna'] ?? '';
                    $fecha_desde = $olimpiada['fecha_desde'] ?? '';
                    $fecha_hasta = $olimpiada['fecha_hasta'] ?? '';
                    $detalles = $olimpiada['detalles'] ?? '';
                    $obs = $olimpiada['obs'] ?? '';
                    ?>
                    <tr>
                        <td><?php echo htmlspecialchars($id); ?></td>
                        <td><?php echo htmlspecialchars($nombre); ?></td>
                        <td><?php echo htmlspecialchars($Interna); ?></td>
                        <td><?php echo htmlspecialchars($fecha_desde); ?></td>
                        <td><?php echo htmlspecialchars($fecha_hasta); ?></td>
                        <td><?php echo htmlspecialchars(substr($detalles, 0, 30)) . (strlen($detalles) > 30 ? '...' : ''); ?></td>
                        <td><?php echo htmlspecialchars(substr($obs, 0, 20)) . (strlen($obs) > 20 ? '...' : ''); ?></td>
                        <td>
                            <!-- Botón editar -->
                            <button class="btn btn-primary btn-sm btn-editar"
                                data-id="<?php echo htmlspecialchars($id); ?>"
                                data-nombre="<?php echo htmlspecialchars($nombre); ?>"
                                data-interna="<?php echo htmlspecialchars($Interna); ?>"
                                data-fecha_desde="<?php echo htmlspecialchars($fecha_desde); ?>"
                                data-fecha_hasta="<?php echo htmlspecialchars($fecha_hasta); ?>"
                                data-detalles="<?php echo htmlspecialchars($detalles); ?>"
                                data-obs="<?php echo htmlspecialchars($obs); ?>"
                                data-bs-toggle="modal" data-bs-target="#modalOlimpiadaEditar">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <!-- Botón eliminar -->
                            <form method="post" style="display:inline;">
                                <input type="hidden" name="id" value="<?php echo htmlspecialchars($id); ?>">
                                <input type="hidden" name="accion" value="eliminar">
                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('¿Eliminar olimpiada?');">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    <?php
                }
            }
        }
        ?>
        </tbody>
    </table>
</div>

<!-- Modal Nueva Olimpiada -->
<div class="modal fade" id="modalOlimpiadaNueva" tabindex="-1" aria-labelledby="modalOlimpiadaNuevaLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <form method="post" class="modal-content" id="formNuevaOlimpiada">
      <input type="hidden" name="accion" value="crear">
      <div class="modal-header">
        <h5 class="modal-title" id="modalOlimpiadaNuevaLabel">Nueva Olimpiada</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="row g-3">
            <div class="col-md-12">
                <label class="form-label">Nombre de la Olimpiada</label>
                <input type="text" id="nombre_nuevo" name="nombre" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label class="form-label">¿Es Interna?</label>
                <select id="interna_nuevo" name="Interna" class="form-select">
                    <option value="S">Sí</option>
                    <option value="N">No</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">Fecha Desde</label>
                <input type="date" id="fecha_desde_nuevo" name="fecha_desde" class="form-control">
            </div>
            <div class="col-md-4">
                <label class="form-label">Fecha Hasta</label>
                <input type="date" id="fecha_hasta_nuevo" name="fecha_hasta" class="form-control">
            </div>
            <div class="col-md-12">
                <label class="form-label">Detalles</label>
                <textarea id="detalles_nuevo" name="detalles" class="form-control" rows="3"></textarea>
            </div>
            <div class="col-md-12">
                <label class="form-label">Observaciones</label>
                <textarea id="obs_nuevo" name="obs" class="form-control" rows="2"></textarea>
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-success">Crear</button>
      </div>
    </form>
  </div>
</div>

<!-- Modal Editar Olimpiada -->
<div class="modal fade" id="modalOlimpiadaEditar" tabindex="-1" aria-labelledby="modalOlimpiadaEditarLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <form method="post" class="modal-content" id="formEditarOlimpiada">
      <input type="hidden" name="accion" value="editar">
      <input type="hidden" name="id" id="editar_id">
      <div class="modal-header">
        <h5 class="modal-title" id="modalOlimpiadaEditarLabel">Editar Olimpiada</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="row g-3">
            <div class="col-md-12">
                <label class="form-label">Nombre de la Olimpiada</label>
                <input type="text" id="nombre_editar" name="nombre" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label class="form-label">¿Es Interna?</label>
                <select id="interna_editar" name="Interna" class="form-select">
                    <option value="S">Sí</option>
                    <option value="N">No</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">Fecha Desde</label>
                <input type="date" id="fecha_desde_editar" name="fecha_desde" class="form-control">
            </div>
            <div class="col-md-4">
                <label class="form-label">Fecha Hasta</label>
                <input type="date" id="fecha_hasta_editar" name="fecha_hasta" class="form-control">
            </div>
            <div class="col-md-12">
                <label class="form-label">Detalles</label>
                <textarea id="detalles_editar" name="detalles" class="form-control" rows="3"></textarea>
            </div>
            <div class="col-md-12">
                <label class="form-label">Observaciones</label>
                <textarea id="obs_editar" name="obs" class="form-control" rows="2"></textarea>
            </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary">Actualizar</button>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar DataTable
    var table = $('#tablaOlimpiadas').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
        },
        lengthMenu: [ [5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"] ],
        pageLength: 25,
        dom: 'lfrtip',
        scrollX: true,
        initComplete: function () {
            this.api().columns().every(function () {
                var column = this;
                if (column.index() === 7) return; // Excluir columna de acciones
                var input = document.createElement("input");
                input.className = "form-control form-control-sm";
                $(input).appendTo($(column.footer()).empty())
                .on('keyup change clear', function () {
                    if (column.search() !== this.value) {
                        column.search(this.value).draw();
                    }
                });
            });
        }
    });

    // Limpiar formulario de nueva olimpiada al abrir modal
    $('#modalOlimpiadaNueva').on('show.bs.modal', function () {
        $('#formNuevaOlimpiada')[0].reset();
    });

    // Rellenar modal de edición
    document.querySelectorAll('.btn-editar').forEach(btn => {
        btn.addEventListener('click', function() {
            document.getElementById('editar_id').value = this.dataset.id;
            document.getElementById('nombre_editar').value = this.dataset.nombre;
            document.getElementById('interna_editar').value = this.dataset.interna;
            document.getElementById('fecha_desde_editar').value = this.dataset.fecha_desde;
            document.getElementById('fecha_hasta_editar').value = this.dataset.fecha_hasta;
            document.getElementById('detalles_editar').value = this.dataset.detalles;
            document.getElementById('obs_editar').value = this.dataset.obs;
        });
    });
});
</script>
