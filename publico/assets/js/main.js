// Este archivo contiene el código JavaScript para la funcionalidad del frontend, como la interacción con el DOM y la gestión de eventos.

document.addEventListener('DOMContentLoaded', function() {
    // Código para inicializar componentes o manejar eventos
    console.log('El documento está listo y el script se ha cargado correctamente.');

    // Ejemplo de manejo de un evento de clic en un botón
    const button = document.getElementById('miBoton');
    if (button) {
        button.addEventListener('click', function() {
            alert('Botón clicado!');
        });
    }
});