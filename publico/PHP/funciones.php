<?php

// -------------------------------------------------------------------------------- Ir a una Página

function ir_a_pagina( $p_pagina )
{
  echo "<script language=\"javascript\">
    window.location.href=\"". $p_pagina . "\";
    </script>";
}



// -------------------------------------------------------------------------------- Mensaje con JS

function mensaje_con_script( $p_mens )
{

    echo '<script>
        $( document ).ready(function() {
        swal("Oops...", "'.$p_mens.'!", "error");
        });
        </script>';

}


// -------------------------------------------------------------------------------- Validar Email

function is_valid_email( $p_email )
{
  return ( false !== filter_var( $p_email , FILTER_VALIDATE_EMAIL ) );
}



// -------------------------------------------------------------------------------- Conectar a una BD

function conectar_a_una_base_de_datos( $p_base_de_datos )
{
    // -------------------------- Parámetros de Base de Datos

    $w_servidor         = SERVIDOR ; 
    $w_usuario          = USUARIO ;
    $w_clave            = CLAVE ;
    $w_base             = $p_base_de_datos ;


    // echo "<br>CONECTAR A UNA BD ---->" . "Datos--> SERV=" . SERVIDOR  . " US=" . USUARIO  . " CLAVE=" .    CLAVE  . " BD= " . $w_base . "<br>";

    

     $conexion = new mysqli($w_servidor, $w_usuario, $w_clave, $w_base);

     if ($conexion->connect_errno > 0) {
         $w_se_conecto = "N";
         $w_codigo_error = $conexion->connect_errno;
         $w_mensaje_error = $conexion->connect_error;
     } else {
         $w_se_conecto = "S";
         $w_codigo_error = 0;
         $w_mensaje_error = "";
     }



    // echo "CONECTAR A UNA BD ---->" . $w_se_conecto . " MENSAJE ERROR=". $w_mensaje_error . "<br>";


    return $w_se_conecto;

    // Close connection
    mysqli_close( $conexion );
}




// Función para generar un token CSRF

function generar_csrf_token() {
if(!isset($_SESSION['csrf_token'])) {
    $csrf_token = bin2hex(random_bytes(32));
    $_SESSION['csrf_token'] = $csrf_token;
} else {
    $csrf_token = $_SESSION['csrf_token'];
}
return $csrf_token;
}



// Validar el token CSRF

function validar_csrf_token($csrf_token) {
if(isset($_SESSION['csrf_token']) && $_SESSION['csrf_token'] === $csrf_token) {
    return true;
} else {
    return false;
}
}



// **************************************************** ENCONTRAR EL NOMBRE EN UNA ENTIDAD

function encontrar_el_nombre_en_una_entidad(  $p_base_de_datos   ,
                                              $p_tabla           ,
                                              $p_codigo_recibido    )
{
    $w_esta_conectada_la_base_de_datos = conectar_a_una_base_de_datos( $p_base_de_datos );

    if  ( $w_esta_conectada_la_base_de_datos == "S" )
    {
        $w_servidor         = SERVIDOR ;
        $w_usuario          = USUARIO ;
        $w_clave            = CLAVE ;
        $w_base             = $p_base_de_datos;

        $conexion = new mysqli( $w_servidor , $w_usuario , $w_clave , $w_base );

        $w_sql = "SELECT * FROM " . $p_tabla .
                        " WHERE id          LIKE  '$p_codigo_recibido'  AND 
                                activo      LIKE 'S'                    AND
                                eliminado   LIKE 'N' ";


        //echo $w_sql . "<br>";
        // mensaje_con_script( $w_sql );

        if ( $w_result = mysqli_query( $conexion , $w_sql ) )
        {
            if (mysqli_num_rows( $w_result ) > 0 )
            {
                // ----------------------------------------------------------- DATOS DE LAS CARRERAS

                $w_row              = mysqli_fetch_array( $w_result );
                $w_nombre           = $w_row[ "nombre" ];
                
                $w_nombre = html_entity_decode( $w_nombre , ENT_QUOTES | ENT_HTML401, "UTF-8");

            }
            else
            {
                $w_nombre = "";
            }
        }
        else
        {
            $w_nombre = "";
        }

    }
    else
    {
        $w_nombre = "";
            
        $w_mens = "No se ha podido establecer conexión con la BD del Sistema";
        mensaje_con_script( $w_mens );
    }

    return $w_nombre;
}


function verificar_email_previo_en_el_sistema( $p_email )
{
    $w_servidor         = SERVIDOR ;
    $w_usuario          = USUARIO ;
    $w_clave            = CLAVE ;
    $w_base             = BD_SISTEMA ;


    // echo "<br><br><br><br>";
    // echo "verificar identificacion en el sistema ----> " . "Datos--> SERV=" . SERVIDOR  . " US=" . USUARIO  .
    //    " CLAVE=" .    CLAVE  . " BD=" . BD_DATOS_GENERALES . "<br>";


    
    $w_conectado =    conectar_a_una_base_de_datos( BD_SISTEMA );

    // echo "CONECTADO= " . $w_conectado . "<br>";
    
    

    $conexion = new mysqli( $w_servidor , $w_usuario , $w_clave , $w_base );

    $w_sql = "SELECT * FROM usuarios
                    WHERE   email  LIKE  '%$p_email%'  AND activo LIKE 'S' AND eliminado LIKE 'N'  ";

    // echo "<br><br><br><br><br>";
    // echo $w_sql . "<br>";

    if( $w_result = mysqli_query( $conexion , $w_sql ) )
    {
        if ( mysqli_num_rows( $w_result ) > 0 )
        {
            // ----------------------------------------------------------- TOMAMOS EL US_COD

            $w_consta           = "S";

        }
        else
        {
            $w_consta           = "N";
        }
    }
    else
    {
        $w_consta           = "N";
    }

    mysqli_close( $conexion );

    return $w_consta;
}



// **************************************************** OBTENER CÓDIGO NUEVO

function obtener_codigo_nuevo_en_una_tabla_por_id( $p_tabla_principal , $p_base_de_datos )
{
    $w_esta_conectada_bd_datos_generales = conectar_a_una_base_de_datos( $p_base_de_datos );

    if  ( $w_esta_conectada_bd_datos_generales == "S" )
    {
        $w_servidor         = SERVIDOR ;
        $w_usuario          = USUARIO ;
        $w_clave            = CLAVE ;
        $w_base             = $p_base_de_datos;

        $conexion         = new mysqli( $w_servidor , $w_usuario , $w_clave , $w_base );

        // $w_sql = "SELECT * FROM " . $p_tabla_principal ;

        $w_sql = "SELECT MAX( id ) AS MAXIMO FROM " . $p_tabla_principal;


        
        
        // echo $w_sql . "<br>";

        


        $w_result = mysqli_query( $conexion , $w_sql );

        $row = mysqli_fetch_row( $w_result );


        // print_r( $row ) . "<BR>";
        // echo "ID MAXIMO= " . $row[ 0 ] . "<br>";


        // $w_num_registros = mysqli_num_rows( $w_result );

        $w_ultimo_id        = $row[ 0 ];

        // $w_codigo_nuevo     = $w_num_registros + 1;

        $w_codigo_nuevo     = $w_ultimo_id + 1;


        // echo $w_codigo_nuevo . "<br>";



        return $w_codigo_nuevo;
    }
    else
    {
        $w_num_registros = 0;

        $w_mens = "Lo sentimos. No se ha establecido conexión con la BD";
        mensaje_con_script( $w_mens );
    }

    return $w_num_registros;
}




// **************************************************** VERIFICAR NÚMERO DE CÉDULA PREVIO EN EL SISTEMA

function verificar_numero_de_cedula_previo_en_el_sistema( $p_nced )
{
    $w_servidor         = SERVIDOR ;
    $w_usuario          = USUARIO ;
    $w_clave            = CLAVE ;
    $w_base             = BD_SISTEMA ;


    // echo "<br><br><br><br>";
    // echo "verificar identificacion en el sistema ----> " . "Datos--> SERV=" . SERVIDOR  . " US=" . USUARIO  .
    //    " CLAVE=" .    CLAVE  . " BD=" . BD_DATOS_GENERALES . "<br>";


    
    $w_conectado =    conectar_a_una_base_de_datos( BD_SISTEMA );

    // echo "CONECTADO= " . $w_conectado . "<br>";
    
    

    $conexion = new mysqli( $w_servidor , $w_usuario , $w_clave , $w_base );

    $w_sql = "SELECT * FROM usuarios
                    WHERE   US_NCED  LIKE  '%$p_nced%'  AND US_ACTIVO LIKE 'S' AND US_ELIMINADO LIKE 'N'  ";


    // echo "<br><br><br><br><br>";
    // echo $w_sql . "<br>";


    if( $w_result = mysqli_query( $conexion , $w_sql ) )
    {
        if ( mysqli_num_rows( $w_result ) > 0 )
        {
            // ----------------------------------------------------------- TOMAMOS EL US_COD

            $w_consta           = "S";

        }
        else
        {
            $w_consta           = "N";
        }
    }
    else
    {
        $w_consta           = "N";
    }

    mysqli_close( $conexion );

    

    return $w_consta;
}



// **************************************************** OBTENER NÚMERO DE REGISTROS EN UNA TABLA

function obtener_numero_de_registros_de_la_tabla( $p_tabla_principal , $p_base_de_datos )
{



    // echo "EN OBTENER NUMERO DE REGISTROS DE LA TABLA= " . $p_tabla_principal . " - " . $p_base_de_datos . "<br>";




    $w_esta_conectada_bd_datos_generales = conectar_a_una_base_de_datos( $p_base_de_datos );

    if  ( $w_esta_conectada_bd_datos_generales == "S" )
    {
        $w_servidor         = SERVIDOR ;
        $w_usuario          = USUARIO ;
        $w_clave            = CLAVE ;
        $w_base             = $p_base_de_datos;

        $conexion = new mysqli( $w_servidor , $w_usuario , $w_clave , $w_base );

        $w_sql = "SELECT * FROM " . $p_tabla_principal ." WHERE activo LIKE 'S' AND eliminado LIKE 'N' ";



        // echo $w_sql . "<br>";



        $w_result = mysqli_query( $conexion , $w_sql );

        $w_num_registros = @mysqli_num_rows( $w_result );
    }
    else
    {
        $w_num_registros = 0;

        $w_mens = "Lo sentimos. No se ha establecido conexión con la BD";
        mensaje_con_script( $w_mens );
    }

    return $w_num_registros;
}



// ********************************* OBTENER NÚMERO DE REGISTROS EN UNA TABLA CON CONDICIÓN

function obtener_numero_de_registros_de_la_tabla_con_condicion(
                                            $p_tabla_principal  ,
                                            $p_base_de_datos    ,
                                            $p_condicion            )
{
    $w_esta_conectada_bd_datos_generales = conectar_a_una_base_de_datos( $p_base_de_datos );

    
    // echo "p_tabla_principal= " . $p_tabla_principal . "<br>"; // REVISION
    // echo "p_base_de_datos= " . $p_base_de_datos . "<br>"; // REVISION
    // echo "p_condicion= " . $p_condicion . "<br>"; // REVISION

     // echo "w_esta_conectada_bd_datos_generales= " . $w_esta_conectada_bd_datos_generales . "<br>"; // REVISION


    if  ( $w_esta_conectada_bd_datos_generales == "S" )
    {
        $w_servidor         = SERVIDOR ;
        $w_usuario          = USUARIO ;
        $w_clave            = CLAVE ;
        $w_base             = $p_base_de_datos;

        $conexion = new mysqli( $w_servidor , $w_usuario , $w_clave , $w_base );


        $w_sql = "SELECT * FROM " . $p_tabla_principal  . " WHERE " . $p_condicion ;



        // echo "<br><br><br>SQL= " . $w_sql . "<br>"; // REVISION (1)

        

        $w_result = mysqli_query( $conexion , $w_sql );

        $w_num_registros = @mysqli_num_rows( $w_result );


        //echo "LEIDO w_num_registros= " . $w_num_registros . "<br>"; // REVISION


    }
    else
    {
        $w_num_registros = 0;

        $w_mens = "Lo sentimos. No se ha establecido conexión con la BD";
        mensaje_con_script( $w_mens );
    }

    return $w_num_registros;
}




// ********************************* OBTENER NÚMERO DE REGISTROS EN UNA TABLA ORDENADA
function obtener_numero_de_registros_de_una_tabla_ordenada(
                                            $p_tabla_principal  ,
                                            $p_base_de_datos    ,
                                            $p_orden                )
{
    $w_esta_conectada_bd_datos_generales = conectar_a_una_base_de_datos( $p_base_de_datos );

    
    // echo "p_tabla_principal= " . $p_tabla_principal . "<br>"; // REVISION
    // echo "p_base_de_datos= " . $p_base_de_datos . "<br>"; // REVISION
    // echo "p_condicion= " . $p_condicion . "<br>"; // REVISION

    // echo "w_esta_conectada_bd_datos_generales= " . $w_esta_conectada_bd_datos_generales . "<br>"; // REVISION


    if  ( $w_esta_conectada_bd_datos_generales == "S" )
    {
        $w_servidor         = SERVIDOR ;
        $w_usuario          = USUARIO ;
        $w_clave            = CLAVE ;
        $w_base             = $p_base_de_datos;

        $conexion = new mysqli( $w_servidor , $w_usuario , $w_clave , $w_base );


        $w_sql = "SELECT * FROM " . $p_tabla_principal  . " ORDER BY " . $p_orden ;



        // echo "<br><br><br>SQL= " . $w_sql . "<br>"; // REVISION (1)

        

        $w_result = mysqli_query( $conexion , $w_sql );

        $w_num_registros = @mysqli_num_rows( $w_result );


        //echo "LEIDO w_num_registros= " . $w_num_registros . "<br>"; // REVISION


    }
    else
    {
        $w_num_registros = 0;

        $w_mens = "Lo sentimos. No se ha establecido conexión con la BD";
        mensaje_con_script( $w_mens );
    }

    return $w_num_registros;
}



function obtener_numero_de_registros_de_una_tabla_por_condicion(
                                            $p_tabla_principal  ,
                                            $p_base_de_datos    ,
                                            $p_condicion                )
{
    $w_esta_conectada_bd_datos_generales = conectar_a_una_base_de_datos( $p_base_de_datos );

    
    // echo "p_tabla_principal= " . $p_tabla_principal . "<br>"; // REVISION
    // echo "p_base_de_datos= " . $p_base_de_datos . "<br>"; // REVISION
    // echo "p_condicion= " . $p_condicion . "<br>"; // REVISION

    // echo "w_esta_conectada_bd_datos_generales= " . $w_esta_conectada_bd_datos_generales . "<br>"; // REVISION


    if  ( $w_esta_conectada_bd_datos_generales == "S" )
    {
        $w_servidor         = SERVIDOR ;
        $w_usuario          = USUARIO ;
        $w_clave            = CLAVE ;
        $w_base             = $p_base_de_datos;

        $conexion = new mysqli( $w_servidor , $w_usuario , $w_clave , $w_base );


        $w_sql = "SELECT * FROM " . $p_tabla_principal  . " WHERE " . $p_condicion ;



        // echo "<br><br><br>SQL= " . $w_sql . "<br>"; // REVISION (1)

        

        $w_result = mysqli_query( $conexion , $w_sql );

        $w_num_registros = @mysqli_num_rows( $w_result );


        //echo "LEIDO w_num_registros= " . $w_num_registros . "<br>"; // REVISION


    }
    else
    {
        $w_num_registros = 0;

        $w_mens = "Lo sentimos. No se ha establecido conexión con la BD";
        mensaje_con_script( $w_mens );
    }

    return $w_num_registros;
}




function obtener_id_de_una_tabla_por_condicion(
                                            $p_tabla_principal,
                                            $p_base_de_datos,
                                            $p_condicion        ) 
{
    $id = null;
    $w_esta_conectada_bd_datos_generales = conectar_a_una_base_de_datos($p_base_de_datos);

    if ($w_esta_conectada_bd_datos_generales == "S") {
        $w_servidor = SERVIDOR;
        $w_usuario  = USUARIO;
        $w_clave    = CLAVE;
        $w_base     = $p_base_de_datos;

        $conexion = new mysqli($w_servidor, $w_usuario, $w_clave, $w_base);

        // Solo selecciona el campo id
        $w_sql = "SELECT id FROM " . $p_tabla_principal . " WHERE " . $p_condicion . " LIMIT 1";

        if ($w_result = mysqli_query($conexion, $w_sql)) {
            if ($row = mysqli_fetch_assoc($w_result)) {
                $id = $row['id'];
            }
        }

        mysqli_close($conexion);
    } else {
        $w_mens = "Lo sentimos. No se ha establecido conexión con la BD";
        mensaje_con_script($w_mens);
    }

    return $id;
}

// -------------------------------------------------------------------------------- Agregar Registro a una Tabla

function agregar_registro_en_una_tabla(   
                                $p_base_de_datos                ,
                                $p_tabla                        ,
                                $p_campos_de_la_tabla           ,
                                $p_valores_de_campos            ,
                                $p_condicion_para_comprobar     ,
                                $p_id                                 )
{
    $w_esta_conectada_la_base_de_datos = conectar_a_una_base_de_datos( $p_base_de_datos );

    if  ( $w_esta_conectada_la_base_de_datos == "S" )
    {
        $w_servidor         = SERVIDOR ; 
        $w_usuario          = USUARIO ;
        $w_clave            = CLAVE ;
        $w_base             = $p_base_de_datos ;

        $conexion = new mysqli( $w_servidor , $w_usuario , $w_clave , $w_base );

        if ( $conexion -> connect_error )
        {
            $w_mens = "Error agregando registro: " . $conn->error;
            mensaje_con_script( $w_mens );

            die("Conexión a BD ha fallado: " . $conexion -> connect_error );
        }
        else
        {
            if ( !empty( $p_condicion_para_comprobar ) )
            {
                // -------------------------------------------------- VERIFICAMOS SI EL REGISTRO YA EXISTE

                $conexion = new mysqli( $w_servidor , $w_usuario , $w_clave , $w_base );

                $w_sql = "SELECT * FROM " . $p_tabla . " WHERE " . $p_condicion_para_comprobar ;






                //echo "<br><br>COMPROBAR= " . $w_sql . "<br>";

                // mensaje_con_script( $w_sql );


                //die();



                $w_result = mysqli_query( $conexion , $w_sql );

                $w_num_registros = @mysqli_num_rows( $w_result );


                // mensaje_con_script( $w_num_registros );

            }
            else
            {
                $w_num_registros = 0;
            }




            if ( $w_num_registros > 0 )
            {
                $w_mens = "Registro Ya Existe";

                // mensaje_con_script( $w_mens );
            }
            else
            {
                // ---------------------------------------------- CREAMOS EL REGISTRO


                $w_orden_insert = "INSERT INTO " . $p_tabla . "(" . $p_campos_de_la_tabla . ")" .
                                        "VALUES " .           "(" . $p_valores_de_campos  . ")" ;



                //b echo "<br><br><br>" . $w_orden_insert . "<br>";
                //die();




                // mensaje_con_script( $w_orden_insert );




                if ( $conexion -> query( $w_orden_insert ) === TRUE)
                {
                    $w_mens = "Registro Nuevo creado";

                    // mensaje_con_script( $w_mens );
                    echo '<script>
                    $( document ).ready(function() {
                    swal("Buen Trabajo!", "'.$w_mens.'!", "success");
                    });
                    </script>';


                    $w_condicion            = " id > 0 ";

                    $w_numero_de_registros  = obtener_numero_de_registros_de_la_tabla_con_condicion(
                                                            "usuarios_logs"     ,
                                                            BD_SISTEMA          ,
                                                            $w_condicion            );

                    $w_nuevo_id_log         = $w_numero_de_registros + 1;


                    $conexion2 = new mysqli( $w_servidor , $w_usuario , $w_clave , BD_SISTEMA );


                    // ------------------------------------- Grabamos Log

                    $g_us_id = $_SESSION[ "g_us_id" ];

                    date_default_timezone_set("America/Guayaquil");

                    if (!empty($_SERVER['HTTP_CLIENT_IP']))   
                    {

                        $ip_address = $_SERVER['HTTP_CLIENT_IP'];

                    }

                    //whether ip is from proxy

                    elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR']))  
                    {

                        $ip_address = $_SERVER['HTTP_X_FORWARDED_FOR'];

                    }

                    //whether ip is from remote address

                    else

                    {

                        $ip_address = $_SERVER['REMOTE_ADDR'];

                    }

                    $w_fecha_log			= @date("Ymd");
                    $w_hora_log				= @date( 'H:i:s' );

                    $p_tabla_log            = "usuarios_logs";

                    $p_campos_de_la_tabla   =   "id"      			    . "," .
                                                "usuario_id"      		. "," .
                                                "accion"      			. "," .
                                                "tabla"      			. "," .
                                                "tabla_id"      		. "," .
                                                "fecha"      			. "," .
                                                "hora"      			. "," .
                                                "ip"      			                      ;

                    $p_valores_de_campos    =   "'" . $w_nuevo_id_log            	. "'" . "," .
                                                "'" . $g_us_id            	        . "'" . "," .
                                                "'" . "CREAR"            	        . "'" . "," .
                                                "'" . $p_tabla                      . "'" . "," .
                                                "'" . $p_id                         . "'" . "," .
                                                "'" . $w_fecha_log                  . "'" . "," . 
                                                "'" . $w_hora_log                   . "'" . "," .
                                                "'" . $ip_address                   . "'" ;

                    $w_orden_insert = "INSERT INTO " . $p_tabla_log . "(" . $p_campos_de_la_tabla . ")" .
                                                "VALUES " .           "(" . $p_valores_de_campos  . ")" ;

                    if ( $conexion2 -> query( $w_orden_insert ) === TRUE)
                    {

                    }


                }
                else
                {
                    $w_mens = "No se ha creado el Registro";

                    /*
                    echo '<script>
                    $( document ).ready(function() {
                    swal("Oops...", "'.$w_mens.'!", "error");
                    });
                    </script>';
                    */


                    // echo $w_mens . " --> " . $w_orden_insert . "<br>";



                    // mensaje_con_script( $w_mens );
                }

            }

        }

        $conexion -> close();
    }
    else
    {
        $w_mens = "No se ha establecido conexión con la BD " ;

        mensaje_con_script( $w_mens );
    }   

}



// -------------------------------------------------------------------------------- Limpieza de Campo de Formulario

function limpieza_de_campo( $p_campo )
{
    if ( !empty( $p_campo ) )
    {
        $p_campo          = htmlspecialchars( $p_campo      );
        $p_campo          = stripslashes( $p_campo      );
        $p_campo          = trim( $p_campo );
        $p_campo          = htmlentities( $p_campo     , ENT_QUOTES , 'UTF-8' );
        @str_replace( "´"    , ""    , $p_campo );
        @str_replace( "´´"   , ""    , $p_campo );


        // ---------------------------------------------------------------- STRING

        if ( @is_string( $p_campo ) == true )
        {
            @$p_campo = filter_var( $p_campo   , FILTER_SANITIZE_STRING );
        }


        // ---------------------------------------------------------------- INT

        if ( @is_int( $p_campo ) == true )
        {
            $p_campo = filter_var( $p_campo   , FILTER_SANITIZE_NUMBER_INT );
        }


        // ---------------------------------------------------------------- EMAIL

        $w_email_ok     = is_valid_email( $p_campo );

        if ( $w_email_ok == 1 )
        {
            $p_campo        = filter_var( $p_campo      , FILTER_SANITIZE_EMAIL  );
        }

    } // ( !empty( $p_campo ) )


    return $p_campo;


}   



// ******************************************************** Verificar Email previo si corresponde al mismo Usuario

function verificar_email_previo_en_el_sistema_por_us_id( $p_us_id , $p_email )
    {
        $w_servidor         = SERVIDOR ;
        $w_usuario          = USUARIO ;
        $w_clave            = CLAVE ;
        $w_base             = BD_SISTEMA ;


        // echo "<br><br><br><br>";
        // echo "verificar identificacion en el sistema ----> " . "Datos--> SERV=" . SERVIDOR  . " US=" . USUARIO  .
        //    " CLAVE=" .    CLAVE  . " BD=" . BD_DATOS_GENERALES . "<br>";


        
        $w_conectado =    conectar_a_una_base_de_datos( BD_SISTEMA );

        // echo "CONECTADO= " . $w_conectado . "<br>";
        
        

        $conexion = new mysqli( $w_servidor , $w_usuario , $w_clave , $w_base );

        $w_sql = "SELECT * FROM usuarios
                        WHERE   id           LIKE '$p_us_id'     AND  
                                email        LIKE  '%$p_email%'  AND 
                                activo       LIKE 'S'            AND 
                                eliminado    LIKE 'N'                ";

        // echo "<br><br><br><br><br>";
        // echo $w_sql . "<br>";

        if( $w_result = mysqli_query( $conexion , $w_sql ) )
        {
            if ( mysqli_num_rows( $w_result ) > 0 )
            {
                // ----------------------------------------------------------- TOMAMOS EL US_COD

                $w_consta           = "S";

            }
            else
            {
                $w_consta           = "N";
            }
        }
        else
        {
            $w_consta           = "N";
        }

        mysqli_close( $conexion );

        

        return $w_consta;
    }




    // -------------------------------------------------------------------------------- Actualizar Registro en una Tabla

function actualizar_registro_en_una_tabla(  
                                            $p_base_de_datos                ,
                                            $p_tabla                        ,
                                            $p_campos_de_la_tabla_y_valores ,
                                            $p_condicion_update             , 
                                            $p_id                               )
{
   $w_esta_conectada_la_base_de_datos = conectar_a_una_base_de_datos( $p_base_de_datos );

    if  ( $w_esta_conectada_la_base_de_datos == "S" )
    {
        $w_servidor         = SERVIDOR ;
        $w_usuario          = USUARIO ;
        $w_clave            = CLAVE ;
        $w_base             = $p_base_de_datos;

        $conexion = new mysqli( $w_servidor , $w_usuario , $w_clave , $w_base );

        //mysqli_set_charset( $conexion , "UTF-8" ) ;

        if ($conexion -> connect_error )
        {
            $w_mens = "Error actualizando registro: " . $conn->error;
            mensaje_con_script( $w_mens );

            die("Conexión a BD ha fallado: " . $conexion -> connect_error );
        }
        else
        {
            if ( !empty( $p_condicion_update ) )
            {
                $w_orden_update       = "UPDATE  " . $p_tabla .
                                        " SET " . $p_campos_de_la_tabla_y_valores .
                                        " WHERE " . $p_condicion_update;

                
                
                // echo "<br><br><br><br>UPDATE= " . $w_orden_update . "<br>";


                echo '<script>
                        $( document ).ready(function() {
                        swal("Buen Trabajo!", "Registro Actualizado con éxito", "success");
                        });
                        </script>';


                $w_condicion            = " id > 0 ";

                $w_numero_de_registros  = obtener_numero_de_registros_de_la_tabla_con_condicion(
                                                                            "usuarios_logs"     ,
                                                                            BD_SISTEMA          ,
                                                                            $w_condicion            );

                $w_nuevo_id_log         = $w_numero_de_registros + 1;


                $conexion2 = new mysqli( $w_servidor , $w_usuario , $w_clave , BD_SISTEMA );


                // ------------------------------------- Grabamos Log

                $g_us_id = $_SESSION[ "g_us_id" ];

                date_default_timezone_set("America/Guayaquil");

                if (!empty($_SERVER['HTTP_CLIENT_IP']))   
                {

                    $ip_address = $_SERVER['HTTP_CLIENT_IP'];

                }

            //whether ip is from proxy

                elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR']))  
                {

                    $ip_address = $_SERVER['HTTP_X_FORWARDED_FOR'];

                }

                //whether ip is from remote address

                else

                {

                    $ip_address = $_SERVER['REMOTE_ADDR'];

                }

                $w_fecha_log			= @date("Ymd");
                $w_hora_log				= @date( 'H:i:s' );

                $p_tabla_log            = "usuarios_logs";

                $p_campos_de_la_tabla   =   "id"      			    . "," .
                                            "usuario_id"      		. "," .
                                            "accion"      			. "," .
                                            "tabla"      			. "," .
                                            "tabla_id"      		. "," .
                                            "fecha"      			. "," .
                                            "hora"      			. "," .
                                            "ip"      			                     ;

                $p_valores_de_campos    =   "'" . $w_nuevo_id_log            	. "'" . "," .
                                            "'" . $g_us_id            	        . "'" . "," .
                                            "'" . "ACTUALIZAR"         	        . "'" . "," .
                                            "'" . $p_tabla                      . "'" . "," .
                                            "'" . $p_id                         . "'" . "," .
                                            "'" . $w_fecha_log                  . "'" . "," . 
                                            "'" . $w_hora_log                   . "'" . "," .
                                            "'" . $ip_address                   . "'" ;

                $w_orden_insert = "INSERT INTO " . $p_tabla_log . "(" . $p_campos_de_la_tabla . ")" .
                                            "VALUES " .           "(" . $p_valores_de_campos  . ")" ;

                // echo $w_orden_insert;


                /*
        
                if ( @$conexion2 -> query( $w_orden_insert ) === TRUE)
                {

                }

                */



            }

            // $w_resultado = $conexion -> query( $w_orden_update ); AQUI
        }

        $conexion -> close();
    }
    else
    {
        $w_mens = "No disponible conexión a BD ";

        mensaje_con_script( $w_mens );
    }
}





// -------------------------------------------------------------------------------- Eliminar Registro de una Tabla


function eliminar_registro_en_una_tabla(  
                                            $p_base_de_datos                ,
                                            $p_tabla                        ,
                                            $p_condicion_delete             ,
                                            $p_id                                 )
{
    $w_esta_conectada_la_base_de_datos = conectar_a_una_base_de_datos( $p_base_de_datos );

    if  ( $w_esta_conectada_la_base_de_datos == "S" )
    {
        $w_servidor         = SERVIDOR ;
        $w_usuario          = USUARIO ;
        $w_clave            = CLAVE ;
        $w_base             = $p_base_de_datos;

        $conexion = new mysqli( $w_servidor , $w_usuario , $w_clave , $w_base );

        if ($conexion -> connect_error )
        {
            $w_mens = "Error eliminando registro: " . $conn->error;
            mensaje_con_script( $w_mens );

            die("Conexión a BD ha fallado: " . $conexion -> connect_error );
        }
        else
    {
            // $w_orden_delete       = "DELETE FROM  " . $p_tabla . " WHERE " . $p_condicion_delete;

            date_default_timezone_set("America/Guayaquil");

            $w_fecha			= @date("Ymd");
            $w_hora				= @date( 'H:i:s' );

            $g_us_id                = $_SESSION[ "g_us_id" ];


            $w_campos_de_la_tabla_y_valores =   "eliminado               = 'S'               , " .
                                                "activo                  = 'N'               , " .

                                                "feliminado             = '$w_fecha'            ,
                                                 heliminado             = '$w_hora'             ,
                                                 elimino                = '$g_us_id'                " ;



            $w_orden_delete       = "UPDATE  " . $p_tabla .
                                        " SET " . $w_campos_de_la_tabla_y_valores .
                                        " WHERE " . $p_condicion_delete;



            // echo "<br><br><br>" . $w_orden_delete . "<br>";



            $w_resultado = $conexion -> query( $w_orden_delete );

            echo '<script>
            $( document ).ready(function() {
            swal("Buen Trabajo!", "Registro Eliminado con éxito", "success");
            });
            </script>';

            $w_condicion            = " id > 0 ";

            $w_numero_de_registros  = obtener_numero_de_registros_de_la_tabla_con_condicion(
                                                                        "usuarios_logs"     ,
                                                                        BD_SISTEMA          ,
                                                                        $w_condicion            );

            $w_nuevo_id_log         = $w_numero_de_registros + 1;


            $conexion2 = new mysqli( $w_servidor , $w_usuario , $w_clave , BD_SISTEMA );


            // ------------------------------------- Grabamos Log

            $g_us_id = $_SESSION[ "g_us_id" ];

            date_default_timezone_set("America/Guayaquil");

            if (!empty($_SERVER['HTTP_CLIENT_IP']))   
            {

                $ip_address = $_SERVER['HTTP_CLIENT_IP'];

            }

        //whether ip is from proxy

            elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR']))  
            {

                $ip_address = $_SERVER['HTTP_X_FORWARDED_FOR'];

            }

        //whether ip is from remote address

            else

            {

                $ip_address = $_SERVER['REMOTE_ADDR'];

            }

            $w_fecha_log			= @date("Ymd");
            $w_hora_log				= @date( 'H:i:s' );

            $p_tabla_log            = "usuarios_logs";

            $p_campos_de_la_tabla   =   "id"      			    . "," .
                                        "usuario_id"      			    . "," .
                                        "accion"      			. "," .
                                        "tabla"      			. "," .
                                        "tabla_id"      		. "," .
                                        "fecha"      			. "," .
                                        "hora"      			. "," .
                                        "ip"      			                     ;

            $p_valores_de_campos    =   "'" . $w_nuevo_id_log            	. "'" . "," .
                                        "'" . $g_us_id            	        . "'" . "," .
                                        "'" . "ELIMINAR"         	        . "'" . "," .
                                        "'" . $p_tabla                      . "'" . "," .
                                        "'" . $p_id                         . "'" . "," .
                                        "'" . $w_fecha_log                  . "'" . "," . 
                                        "'" . $w_hora_log                   . "'" . "," .
                                        "'" . $ip_address                   . "'" ;

            $w_orden_insert = "INSERT INTO " . $p_tabla_log . "(" . $p_campos_de_la_tabla . ")" .
                                        "VALUES " .           "(" . $p_valores_de_campos  . ")" ;
    
            if ( $conexion2 -> query( $w_orden_insert ) === TRUE)
            {

            }


        }

        $conexion -> close();
    }
    else
    {
        $w_mens = "No se puede acceder a la BD ";

        mensaje_con_script( $w_mens );
    }
}





?>