-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost:3306
-- Tiempo de generación: 24-06-2025 a las 12:20:30
-- Versión del servidor: 8.0.30
-- Versión de PHP: 8.2.27

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `opedag`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `agenda`
--

CREATE TABLE `agenda` (
  `id` bigint NOT NULL,
  `etapa_id` bigint DEFAULT NULL,
  `rol_id` bigint DEFAULT NULL,
  `tarea` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci,
  `status_agenda_id` bigint DEFAULT NULL,
  `activo` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'S',
  `eliminado` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'N',
  `obs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `carreras`
--

CREATE TABLE `carreras` (
  `id` bigint NOT NULL,
  `nombre` varchar(100) DEFAULT NULL,
  `activo` varchar(1) DEFAULT 'S',
  `eliminado` varchar(1) DEFAULT 'N',
  `obs` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `competencias`
--

CREATE TABLE `competencias` (
  `id` bigint NOT NULL,
  `nombre` varchar(100) DEFAULT NULL,
  `fase_id` bigint DEFAULT NULL,
  `activo` varchar(1) DEFAULT 'S',
  `eliminado` varchar(1) DEFAULT 'N',
  `obs` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `competencia_equipos`
--

CREATE TABLE `competencia_equipos` (
  `id` bigint NOT NULL,
  `competencia_id` bigint DEFAULT NULL,
  `equipo_id` bigint DEFAULT NULL,
  `puntaje` decimal(10,2) DEFAULT NULL,
  `activo` varchar(1) DEFAULT 'S',
  `eliminado` varchar(1) DEFAULT 'N',
  `obs` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `entidades`
--

CREATE TABLE `entidades` (
  `id` bigint NOT NULL,
  `nombre` varchar(100) DEFAULT NULL,
  `nombre_de_tabla` varchar(50) DEFAULT NULL,
  `activo` varchar(1) DEFAULT 'S',
  `eliminado` varchar(1) DEFAULT 'N',
  `obs` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Volcado de datos para la tabla `entidades`
--

INSERT INTO `entidades` (`id`, `nombre`, `nombre_de_tabla`, `activo`, `eliminado`, `obs`) VALUES
(1, 'Usuarios', 'usuarios', 'S', 'N', NULL),
(2, 'entidades', 'entidades', 'S', 'N', NULL),
(3, 'Roles', 'roles', 'S', 'N', NULL),
(4, 'Rol y Opciones', 'rol_opciones', 'S', 'N', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `equipos`
--

CREATE TABLE `equipos` (
  `id` bigint NOT NULL,
  `nombre` varchar(100) DEFAULT NULL,
  `activo` varchar(1) DEFAULT 'S',
  `eliminado` varchar(1) DEFAULT 'N',
  `obs` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `equipo_estudiantes`
--

CREATE TABLE `equipo_estudiantes` (
  `id` bigint NOT NULL,
  `equipo_id` bigint DEFAULT NULL,
  `estudiante_id` bigint DEFAULT NULL,
  `activo` varchar(1) DEFAULT 'S',
  `eliminado` varchar(1) DEFAULT 'N',
  `obs` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `estudiantes`
--

CREATE TABLE `estudiantes` (
  `id` bigint NOT NULL,
  `usuario_id` bigint DEFAULT NULL,
  `carrera_id` bigint DEFAULT NULL,
  `activo` varchar(1) DEFAULT 'S',
  `eliminado` varchar(1) DEFAULT 'N',
  `obs` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `etapas`
--

CREATE TABLE `etapas` (
  `id` bigint NOT NULL,
  `nombre` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `fecha_desde` date DEFAULT NULL,
  `hora_desde` time DEFAULT NULL,
  `fecha_hasta` date DEFAULT NULL,
  `hora_hasta` time DEFAULT NULL,
  `activo` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'S',
  `eliminado` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'N',
  `obs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `fases`
--

CREATE TABLE `fases` (
  `id` bigint NOT NULL,
  `nombre` varchar(100) DEFAULT NULL,
  `fecha_desde` date DEFAULT NULL,
  `hora_desde` time DEFAULT NULL,
  `fecha_hasta` date DEFAULT NULL,
  `hora_hasta` time DEFAULT NULL,
  `activo` varchar(1) DEFAULT 'S',
  `eliminado` varchar(1) DEFAULT 'N',
  `obs` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `log_accesos`
--

CREATE TABLE `log_accesos` (
  `id` bigint NOT NULL,
  `usuario_id` bigint DEFAULT NULL,
  `fecha_acceso` date DEFAULT NULL,
  `hora_acceso` time DEFAULT NULL,
  `ip_acceso` varchar(45) DEFAULT NULL,
  `activo` varchar(1) DEFAULT 'S',
  `obs` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `log_entidades`
--

CREATE TABLE `log_entidades` (
  `id` bigint NOT NULL,
  `entidad_id` bigint DEFAULT NULL,
  `evento_crud` varchar(50) DEFAULT NULL,
  `fecha_evento_crud` date DEFAULT NULL,
  `hora_evento_crud` time DEFAULT NULL,
  `activo` varchar(1) DEFAULT 'S',
  `obs` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `opciones`
--

CREATE TABLE `opciones` (
  `id` bigint NOT NULL,
  `nombre` varchar(100) DEFAULT NULL,
  `orden` int DEFAULT NULL,
  `activo` varchar(1) DEFAULT 'S',
  `eliminado` varchar(1) DEFAULT 'N',
  `obs` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Volcado de datos para la tabla `opciones`
--

INSERT INTO `opciones` (`id`, `nombre`, `orden`, `activo`, `eliminado`, `obs`) VALUES
(1, 'Usuarios', NULL, 'S', 'N', NULL),
(2, 'Auditoría', NULL, 'S', 'N', NULL),
(3, 'Datos Generales', NULL, 'S', 'N', NULL),
(4, 'Equipos', NULL, 'S', 'N', NULL),
(5, 'Competencias', NULL, 'S', 'N', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `roles`
--

CREATE TABLE `roles` (
  `id` bigint NOT NULL,
  `nombre` varchar(100) DEFAULT NULL,
  `activo` varchar(1) DEFAULT 'S',
  `eliminado` varchar(1) DEFAULT 'N',
  `obs` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Volcado de datos para la tabla `roles`
--

INSERT INTO `roles` (`id`, `nombre`, `activo`, `eliminado`, `obs`) VALUES
(1, 'Admin', 'S', 'N', NULL),
(2, 'Coordinador General', 'S', 'N', NULL),
(3, 'Docente Carrera', 'S', 'N', NULL),
(4, 'Estudiante Carrera', 'S', 'N', NULL),
(5, 'Juez Competencia', 'S', 'N', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `rol_opciones`
--

CREATE TABLE `rol_opciones` (
  `id` bigint NOT NULL,
  `opcion_id` bigint DEFAULT NULL,
  `rol_id` bigint DEFAULT NULL,
  `entidad_id` bigint DEFAULT NULL,
  `listar` varchar(1) DEFAULT NULL,
  `crud_crear` varchar(1) DEFAULT NULL,
  `crud_leer` varchar(1) DEFAULT NULL,
  `crud_actualizar` varchar(1) DEFAULT NULL,
  `crud_eliminar` varchar(1) DEFAULT NULL,
  `activo` varchar(1) DEFAULT 'S',
  `eliminado` varchar(1) DEFAULT 'N',
  `obs` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Volcado de datos para la tabla `rol_opciones`
--

INSERT INTO `rol_opciones` (`id`, `opcion_id`, `rol_id`, `entidad_id`, `listar`, `crud_crear`, `crud_leer`, `crud_actualizar`, `crud_eliminar`, `activo`, `eliminado`, `obs`) VALUES
(1, 1, 1, 1, 'S', 'S', 'S', 'S', 'S', 'S', 'N', NULL),
(2, 1, 1, 2, 'S', 'S', 'S', 'S', 'S', 'S', 'N', NULL),
(3, 1, 1, 3, 'S', 'S', 'S', 'S', 'S', 'S', 'N', NULL),
(4, 1, 1, 4, 'S', 'S', 'S', 'S', 'S', 'S', 'N', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `usuarios`
--

CREATE TABLE `usuarios` (
  `id` bigint NOT NULL,
  `apellidos` varchar(100) DEFAULT NULL,
  `nombres` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `rol_id` bigint DEFAULT NULL,
  `foto` longtext,
  `activo` varchar(1) DEFAULT 'S',
  `eliminado` varchar(1) DEFAULT 'N',
  `obs` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Volcado de datos para la tabla `usuarios`
--

INSERT INTO `usuarios` (`id`, `apellidos`, `nombres`, `email`, `password`, `rol_id`, `foto`, `activo`, `eliminado`, `obs`) VALUES
(1, 'Vera Mosquera', 'Jorge Francisco', '<EMAIL>', '18d532e9b45c72b88deec75bdc203a2b', 1, NULL, 'S', 'N', NULL);

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `agenda`
--
ALTER TABLE `agenda`
  ADD PRIMARY KEY (`id`),
  ADD KEY `etapa_id` (`etapa_id`),
  ADD KEY `rol_id` (`rol_id`);

--
-- Indices de la tabla `carreras`
--
ALTER TABLE `carreras`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `competencias`
--
ALTER TABLE `competencias`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fase_id` (`fase_id`);

--
-- Indices de la tabla `competencia_equipos`
--
ALTER TABLE `competencia_equipos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `competencia_id` (`competencia_id`),
  ADD KEY `equipo_id` (`equipo_id`);

--
-- Indices de la tabla `entidades`
--
ALTER TABLE `entidades`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `equipos`
--
ALTER TABLE `equipos`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `equipo_estudiantes`
--
ALTER TABLE `equipo_estudiantes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `equipo_id` (`equipo_id`),
  ADD KEY `estudiante_id` (`estudiante_id`);

--
-- Indices de la tabla `estudiantes`
--
ALTER TABLE `estudiantes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `usuario_id` (`usuario_id`),
  ADD KEY `carrera_id` (`carrera_id`);

--
-- Indices de la tabla `etapas`
--
ALTER TABLE `etapas`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `fases`
--
ALTER TABLE `fases`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `log_accesos`
--
ALTER TABLE `log_accesos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `usuario_id` (`usuario_id`);

--
-- Indices de la tabla `log_entidades`
--
ALTER TABLE `log_entidades`
  ADD PRIMARY KEY (`id`),
  ADD KEY `entidad_id` (`entidad_id`);

--
-- Indices de la tabla `opciones`
--
ALTER TABLE `opciones`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `rol_opciones`
--
ALTER TABLE `rol_opciones`
  ADD PRIMARY KEY (`id`),
  ADD KEY `opcion_id` (`opcion_id`),
  ADD KEY `rol_id` (`rol_id`),
  ADD KEY `entidad_id` (`entidad_id`);

--
-- Indices de la tabla `usuarios`
--
ALTER TABLE `usuarios`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `rol_id` (`rol_id`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `agenda`
--
ALTER TABLE `agenda`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `carreras`
--
ALTER TABLE `carreras`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `competencias`
--
ALTER TABLE `competencias`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `competencia_equipos`
--
ALTER TABLE `competencia_equipos`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `entidades`
--
ALTER TABLE `entidades`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT de la tabla `equipos`
--
ALTER TABLE `equipos`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `equipo_estudiantes`
--
ALTER TABLE `equipo_estudiantes`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `estudiantes`
--
ALTER TABLE `estudiantes`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `etapas`
--
ALTER TABLE `etapas`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `fases`
--
ALTER TABLE `fases`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `log_accesos`
--
ALTER TABLE `log_accesos`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `log_entidades`
--
ALTER TABLE `log_entidades`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `opciones`
--
ALTER TABLE `opciones`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT de la tabla `roles`
--
ALTER TABLE `roles`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT de la tabla `rol_opciones`
--
ALTER TABLE `rol_opciones`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT de la tabla `usuarios`
--
ALTER TABLE `usuarios`
  MODIFY `id` bigint NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `agenda`
--
ALTER TABLE `agenda`
  ADD CONSTRAINT `agenda_ibfk_1` FOREIGN KEY (`etapa_id`) REFERENCES `etapas` (`id`),
  ADD CONSTRAINT `agenda_ibfk_2` FOREIGN KEY (`rol_id`) REFERENCES `roles` (`id`);

--
-- Filtros para la tabla `competencias`
--
ALTER TABLE `competencias`
  ADD CONSTRAINT `competencias_ibfk_1` FOREIGN KEY (`fase_id`) REFERENCES `fases` (`id`);

--
-- Filtros para la tabla `competencia_equipos`
--
ALTER TABLE `competencia_equipos`
  ADD CONSTRAINT `competencia_equipos_ibfk_1` FOREIGN KEY (`competencia_id`) REFERENCES `competencias` (`id`),
  ADD CONSTRAINT `competencia_equipos_ibfk_2` FOREIGN KEY (`equipo_id`) REFERENCES `equipos` (`id`);

--
-- Filtros para la tabla `equipo_estudiantes`
--
ALTER TABLE `equipo_estudiantes`
  ADD CONSTRAINT `equipo_estudiantes_ibfk_1` FOREIGN KEY (`equipo_id`) REFERENCES `equipos` (`id`),
  ADD CONSTRAINT `equipo_estudiantes_ibfk_2` FOREIGN KEY (`estudiante_id`) REFERENCES `estudiantes` (`id`);

--
-- Filtros para la tabla `estudiantes`
--
ALTER TABLE `estudiantes`
  ADD CONSTRAINT `estudiantes_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`),
  ADD CONSTRAINT `estudiantes_ibfk_2` FOREIGN KEY (`carrera_id`) REFERENCES `carreras` (`id`);

--
-- Filtros para la tabla `log_accesos`
--
ALTER TABLE `log_accesos`
  ADD CONSTRAINT `log_accesos_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`);

--
-- Filtros para la tabla `log_entidades`
--
ALTER TABLE `log_entidades`
  ADD CONSTRAINT `log_entidades_ibfk_1` FOREIGN KEY (`entidad_id`) REFERENCES `entidades` (`id`);

--
-- Filtros para la tabla `rol_opciones`
--
ALTER TABLE `rol_opciones`
  ADD CONSTRAINT `rol_opciones_ibfk_1` FOREIGN KEY (`opcion_id`) REFERENCES `opciones` (`id`),
  ADD CONSTRAINT `rol_opciones_ibfk_2` FOREIGN KEY (`rol_id`) REFERENCES `roles` (`id`),
  ADD CONSTRAINT `rol_opciones_ibfk_3` FOREIGN KEY (`entidad_id`) REFERENCES `entidades` (`id`);

--
-- Filtros para la tabla `usuarios`
--
ALTER TABLE `usuarios`
  ADD CONSTRAINT `usuarios_ibfk_1` FOREIGN KEY (`rol_id`) REFERENCES `roles` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
