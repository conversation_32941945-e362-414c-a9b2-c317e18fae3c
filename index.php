<?php
session_start();

require_once 'config/config.php';

$request = $_GET['url'] ?? '';
$params = explode('/', $request);

if (empty($params[0])) 
{
    require 'frontend/controladores/controlador_inicio.php';
} 
else 
{
    switch ($params[0]) 
    {
        case 'panel':
        require 'backend/controladores/controlador_panel.php';
        break;
    
        case 'usuarios':
            require 'backend/controladores/controlador_usuario.php';
            break;

        case 'roles':
            require 'backend/controladores/controlador_roles.php';
            break;

        case 'usuarios_roles':
            require 'backend/controladores/controlador_usuarios_roles.php';
            break;
    
        default:
            http_response_code(404);
            echo "404 Not Found";
    }
}

?>