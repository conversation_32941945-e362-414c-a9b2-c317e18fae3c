<?php
session_start();

require_once 'config/config.php';

$request = $_GET['url'] ?? '';
$params = explode('/', $request);

if (empty($params[0])) 
{
    require 'frontend/controladores/controlador_inicio.php';
} 
else 
{
    switch ($params[0]) 
    {
        // ------------------- Usuarios

        case 'panel':
        require 'backend/controladores/controlador_panel.php';
        break;
    
        case 'usuarios':
            require 'backend/controladores/controlador_usuario.php';
            break;

        case 'roles':
            require 'backend/controladores/controlador_roles.php';
            break;

        case 'usuarios_roles':
            require 'backend/controladores/controlador_usuarios_roles.php';
            break;

        
        
        // ------------------- Agenda General

        case 'tipos_eventos':
            require 'backend/controladores/controlador_tipos_eventos.php';
            break;

        case 'eventos':
            require 'backend/controladores/controlador_eventos.php';
            break;


        // ------------------- <PERSON><PERSON>es

        case 'olimpiada':
            require 'backend/controladores/controlador_olimpiada.php';
            break;

        case 'tipos_entidades':
            require 'backend/controladores/controlador_tipos_entidades.php';
            break;

        case 'entidades':
            require 'backend/controladores/controlador_entidades.php';
            break;

        case 'carreras':
            require 'backend/controladores/controlador_carreras.php';
            break;

        case 'asignaturas':
            require 'backend/controladores/controlador_asignaturas.php';
            break;
        
        case 'asignatura_olimpiada':
            require 'backend/controladores/controlador_asignatura_olimpiada.php';
            break;

        case 'estudiantes':
            require 'backend/controladores/controlador_estudiantes.php';
            break;

        // ------------------- Competencias


        case 'equipos':
            require 'backend/controladores/controlador_equipos.php';
            break;

        case 'estudiantes_por_equipo':
            require 'backend/controladores/controlador_estudiantes_por_equipo.php';
            break;



            

            // ------------------- Sistema

        case 'auditoria':
            require 'backend/controladores/controlador_auditoria.php';
            break;

        case 'auditoria_sesiones':
            require 'backend/controladores/controlador_auditoria_sesiones.php';
            break;

        default:
            http_response_code(404);
            echo "404 Not Found";
    }
}

?>