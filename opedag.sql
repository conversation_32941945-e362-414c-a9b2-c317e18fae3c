-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost:3306
-- Tiempo de generación: 03-07-2025 a las 20:53:56
-- Versión del servidor: 8.0.30
-- Versión de PHP: 8.2.27

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";

START TRANSACTION;

SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */
;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */
;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */
;
/*!40101 SET NAMES utf8mb4 */
;

--
-- Base de datos: `opedag`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `agenda`
--

CREATE TABLE `agenda` (
    `id` bigint NOT NULL,
    `etapa_id` bigint DEFAULT NULL,
    `rol_id` bigint DEFAULT NULL,
    `tarea` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci,
    `status_agenda_id` bigint DEFAULT NULL,
    `activo` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'S',
    `eliminado` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'N',
    `obs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `auditoria_crud`
--

CREATE TABLE `auditoria_crud` (
    `id` bigint NOT NULL,
    `usuario_id` bigint DEFAULT NULL,
    `tabla` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `operacion` enum(
        'CREATE',
        'READ',
        'UPDATE',
        'DELETE'
    ) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `registro_id` bigint DEFAULT NULL,
    `datos_anteriores` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci,
    `datos_nuevos` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci,
    `fecha_hora` datetime DEFAULT NULL,
    `programa` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci,
    `activo` enum('S', 'N') CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'S',
    `eliminado` enum('S', 'N') CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'N',
    `obs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `auditoria_sesiones`
--

CREATE TABLE `auditoria_sesiones` (
    `id` bigint NOT NULL,
    `usuario_id` bigint DEFAULT NULL,
    `tipo_evento` enum('INGRESO', 'SALIDA') CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `fecha_hora` datetime DEFAULT NULL,
    `ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci,
    `activo` enum('S', 'N') CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'S',
    `eliminado` enum('S', 'N') CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'N',
    `obs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `carreras`
--

CREATE TABLE `carreras` (
    `id` bigint NOT NULL,
    `nombre` varchar(100) DEFAULT NULL,
    `activo` varchar(1) DEFAULT 'S',
    `eliminado` varchar(1) DEFAULT 'N',
    `obs` text
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `competencias`
--

CREATE TABLE `competencias` (
    `id` bigint NOT NULL,
    `nombre` varchar(100) DEFAULT NULL,
    `fase_id` bigint DEFAULT NULL,
    `activo` varchar(1) DEFAULT 'S',
    `eliminado` varchar(1) DEFAULT 'N',
    `obs` text
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `competencia_equipos`
--

CREATE TABLE `competencia_equipos` (
    `id` bigint NOT NULL,
    `competencia_id` bigint DEFAULT NULL,
    `equipo_id` bigint DEFAULT NULL,
    `puntaje` decimal(10, 2) DEFAULT NULL,
    `activo` varchar(1) DEFAULT 'S',
    `eliminado` varchar(1) DEFAULT 'N',
    `obs` text
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `equipos`
--

CREATE TABLE `equipos` (
    `id` bigint NOT NULL,
    `nombre` varchar(100) DEFAULT NULL,
    `activo` varchar(1) DEFAULT 'S',
    `eliminado` varchar(1) DEFAULT 'N',
    `obs` text
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `equipo_estudiantes`
--

CREATE TABLE `equipo_estudiantes` (
    `id` bigint NOT NULL,
    `equipo_id` bigint DEFAULT NULL,
    `estudiante_id` bigint DEFAULT NULL,
    `activo` varchar(1) DEFAULT 'S',
    `eliminado` varchar(1) DEFAULT 'N',
    `obs` text
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `estudiantes`
--

CREATE TABLE `estudiantes` (
    `id` bigint NOT NULL,
    `usuario_id` bigint DEFAULT NULL,
    `carrera_id` bigint DEFAULT NULL,
    `activo` varchar(1) DEFAULT 'S',
    `eliminado` varchar(1) DEFAULT 'N',
    `obs` text
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `etapas`
--

CREATE TABLE `etapas` (
    `id` bigint NOT NULL,
    `nombre` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `fecha_desde` date DEFAULT NULL,
    `hora_desde` time DEFAULT NULL,
    `fecha_hasta` date DEFAULT NULL,
    `hora_hasta` time DEFAULT NULL,
    `activo` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'S',
    `eliminado` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'N',
    `obs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `fases`
--

CREATE TABLE `fases` (
    `id` bigint NOT NULL,
    `nombre` varchar(100) DEFAULT NULL,
    `fecha_desde` date DEFAULT NULL,
    `hora_desde` time DEFAULT NULL,
    `fecha_hasta` date DEFAULT NULL,
    `hora_hasta` time DEFAULT NULL,
    `activo` varchar(1) DEFAULT 'S',
    `eliminado` varchar(1) DEFAULT 'N',
    `obs` text
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `roles`
--

CREATE TABLE `roles` (
    `id` bigint NOT NULL,
    `nombre` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `activo` enum('S', 'N') CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'S',
    `eliminado` enum('S', 'N') CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'N',
    `obs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `roles`
--

INSERT INTO
    `roles` (
        `id`,
        `nombre`,
        `activo`,
        `eliminado`,
        `obs`
    )
VALUES (1, 'Admin', 'S', 'N', NULL),
    (
        2,
        'Coordinador General',
        'S',
        'N',
        NULL
    ),
    (
        3,
        'Docente Carrera',
        'S',
        'N',
        NULL
    ),
    (
        4,
        'Estudiante Carrera',
        'S',
        'N',
        NULL
    ),
    (
        5,
        'Juez Competencia',
        'S',
        'N',
        NULL
    ),
    (
        6,
        'Administrador',
        'S',
        'N',
        NULL
    ),
    (7, 'qqqqq', 'S', 'N', NULL),
    (
        8,
        'xxxx',
        'S',
        'N',
        'xfg jgjhghgf'
    );

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `roles_por_usuario`
--

CREATE TABLE `roles_por_usuario` (
    `id` bigint NOT NULL,
    `usuario_id` bigint DEFAULT NULL,
    `rol_id` bigint DEFAULT NULL,
    `activo` enum('S', 'N') CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'S',
    `eliminado` enum('S', 'N') CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'N',
    `obs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `roles_por_usuario`
--

INSERT INTO
    `roles_por_usuario` (
        `id`,
        `usuario_id`,
        `rol_id`,
        `activo`,
        `eliminado`,
        `obs`
    )
VALUES (1, 1, 1, 'S', 'N', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `usuarios`
--

CREATE TABLE `usuarios` (
    `id` bigint NOT NULL,
    `apellidos` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `nombres` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `contrasena` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `email_institucional` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `email_alterno` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `sexo` enum('M', 'F') CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `foto` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci,
    `estado_civil` enum(
        'Soltero(a)',
        'Casado(a)',
        'Viudo(a)',
        'Divorciado(a)',
        'UnionLibre'
    ) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `telefono` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `celular` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
    `activo` enum('S', 'N') CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'S',
    `eliminado` enum('S', 'N') CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT 'N',
    `obs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `usuarios`
--

INSERT INTO
    `usuarios` (
        `id`,
        `apellidos`,
        `nombres`,
        `contrasena`,
        `email_institucional`,
        `email_alterno`,
        `sexo`,
        `foto`,
        `estado_civil`,
        `telefono`,
        `celular`,
        `activo`,
        `eliminado`,
        `obs`
    )
VALUES (
        1,
        'Vera Mosquera',
        'Jorge Francisco',
        '$2a$12$bdvrX0DnaiCCcSSEbhKCgeAFwWgFhOfHhXkwGjgrOWRdxk5Z0GD6.',
        '<EMAIL>',
        '<EMAIL>',
        'M',
        NULL,
        'Casado(a)',
        '',
        '',
        'S',
        'N',
        NULL
    ),
    (
        2,
        'apell 1',
        'nom 1',
        NULL,
        '<EMAIL>',
        '<EMAIL>',
        'M',
        NULL,
        'Casado(a)',
        '1111',
        '1111',
        'S',
        'N',
        NULL
    ),
    (
        3,
        'xxxx fdfdf',
        'xxx sgfgsfd',
        NULL,
        '<EMAIL>',
        '<EMAIL>',
        'F',
        NULL,
        'Soltero(a)',
        '222xxx',
        'fdf3432',
        'N',
        'S',
        NULL
    ),
    (
        8,
        '',
        '',
        NULL,
        '',
        '',
        NULL,
        NULL,
        NULL,
        '',
        '',
        'N',
        'S',
        NULL
    ),
    (
        12,
        'apell 2',
        'nom 2',
        NULL,
        '<EMAIL>',
        '<EMAIL>',
        'M',
        NULL,
        'Casado(a)',
        '',
        '',
        'S',
        'N',
        NULL
    ),
    (
        14,
        'apell 4',
        'nom 4',
        NULL,
        '<EMAIL>',
        '<EMAIL>',
        NULL,
        NULL,
        NULL,
        '',
        '',
        'S',
        'N',
        NULL
    ),
    (
        15,
        'xsafdfd',
        'sfdgfhfdg',
        NULL,
        '<EMAIL>',
        NULL,
        NULL,
        NULL,
        NULL,
        '',
        '',
        'N',
        'S',
        NULL
    ),
    (
        16,
        'yyyy',
        'dfdfds',
        NULL,
        '<EMAIL>',
        NULL,
        NULL,
        NULL,
        NULL,
        '',
        '',
        'S',
        'N',
        NULL
    );

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `agenda`
--
ALTER TABLE `agenda`
ADD PRIMARY KEY (`id`),
ADD KEY `etapa_id` (`etapa_id`),
ADD KEY `rol_id` (`rol_id`);

--
-- Indices de la tabla `auditoria_crud`
--
ALTER TABLE `auditoria_crud`
ADD PRIMARY KEY (`id`),
ADD KEY `usuario_id` (`usuario_id`);

--
-- Indices de la tabla `auditoria_sesiones`
--
ALTER TABLE `auditoria_sesiones`
ADD PRIMARY KEY (`id`),
ADD KEY `usuario_id` (`usuario_id`);

--
-- Indices de la tabla `carreras`
--
ALTER TABLE `carreras` ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `competencias`
--
ALTER TABLE `competencias`
ADD PRIMARY KEY (`id`),
ADD KEY `fase_id` (`fase_id`);

--
-- Indices de la tabla `competencia_equipos`
--
ALTER TABLE `competencia_equipos`
ADD PRIMARY KEY (`id`),
ADD KEY `competencia_id` (`competencia_id`),
ADD KEY `equipo_id` (`equipo_id`);

--
-- Indices de la tabla `equipos`
--
ALTER TABLE `equipos` ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `equipo_estudiantes`
--
ALTER TABLE `equipo_estudiantes`
ADD PRIMARY KEY (`id`),
ADD KEY `equipo_id` (`equipo_id`),
ADD KEY `estudiante_id` (`estudiante_id`);

--
-- Indices de la tabla `estudiantes`
--
ALTER TABLE `estudiantes`
ADD PRIMARY KEY (`id`),
ADD KEY `usuario_id` (`usuario_id`),
ADD KEY `carrera_id` (`carrera_id`);

--
-- Indices de la tabla `etapas`
--
ALTER TABLE `etapas` ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `fases`
--
ALTER TABLE `fases` ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `roles`
--
ALTER TABLE `roles` ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `roles_por_usuario`
--
ALTER TABLE `roles_por_usuario`
ADD PRIMARY KEY (`id`),
ADD KEY `usuario_id` (`usuario_id`),
ADD KEY `rol_id` (`rol_id`);

--
-- Indices de la tabla `usuarios`
--
ALTER TABLE `usuarios`
ADD PRIMARY KEY (`id`),
ADD UNIQUE KEY `email_institucional` (
    `email_institucional`,
    `activo`,
    `eliminado`
),
ADD UNIQUE KEY `email_alterno` (
    `email_alterno`,
    `activo`,
    `eliminado`
);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `agenda`
--
ALTER TABLE `agenda` MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `auditoria_crud`
--
ALTER TABLE `auditoria_crud`
MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `auditoria_sesiones`
--
ALTER TABLE `auditoria_sesiones`
MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `carreras`
--
ALTER TABLE `carreras` MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `competencias`
--
ALTER TABLE `competencias`
MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `competencia_equipos`
--
ALTER TABLE `competencia_equipos`
MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `equipos`
--
ALTER TABLE `equipos` MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `equipo_estudiantes`
--
ALTER TABLE `equipo_estudiantes`
MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `estudiantes`
--
ALTER TABLE `estudiantes` MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `etapas`
--
ALTER TABLE `etapas` MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `fases`
--
ALTER TABLE `fases` MODIFY `id` bigint NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `roles`
--
ALTER TABLE `roles`
MODIFY `id` bigint NOT NULL AUTO_INCREMENT,
AUTO_INCREMENT = 9;

--
-- AUTO_INCREMENT de la tabla `roles_por_usuario`
--
ALTER TABLE `roles_por_usuario`
MODIFY `id` bigint NOT NULL AUTO_INCREMENT,
AUTO_INCREMENT = 2;

--
-- AUTO_INCREMENT de la tabla `usuarios`
--
ALTER TABLE `usuarios`
MODIFY `id` bigint NOT NULL AUTO_INCREMENT,
AUTO_INCREMENT = 17;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `agenda`
--
ALTER TABLE `agenda`
ADD CONSTRAINT `agenda_ibfk_1` FOREIGN KEY (`etapa_id`) REFERENCES `etapas` (`id`),
ADD CONSTRAINT `agenda_ibfk_2` FOREIGN KEY (`rol_id`) REFERENCES `roles` (`id`);

--
-- Filtros para la tabla `auditoria_crud`
--
ALTER TABLE `auditoria_crud`
ADD CONSTRAINT `auditoria_crud_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`);

--
-- Filtros para la tabla `auditoria_sesiones`
--
ALTER TABLE `auditoria_sesiones`
ADD CONSTRAINT `auditoria_sesiones_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`);

--
-- Filtros para la tabla `competencias`
--
ALTER TABLE `competencias`
ADD CONSTRAINT `competencias_ibfk_1` FOREIGN KEY (`fase_id`) REFERENCES `fases` (`id`);

--
-- Filtros para la tabla `competencia_equipos`
--
ALTER TABLE `competencia_equipos`
ADD CONSTRAINT `competencia_equipos_ibfk_1` FOREIGN KEY (`competencia_id`) REFERENCES `competencias` (`id`),
ADD CONSTRAINT `competencia_equipos_ibfk_2` FOREIGN KEY (`equipo_id`) REFERENCES `equipos` (`id`);

--
-- Filtros para la tabla `equipo_estudiantes`
--
ALTER TABLE `equipo_estudiantes`
ADD CONSTRAINT `equipo_estudiantes_ibfk_1` FOREIGN KEY (`equipo_id`) REFERENCES `equipos` (`id`),
ADD CONSTRAINT `equipo_estudiantes_ibfk_2` FOREIGN KEY (`estudiante_id`) REFERENCES `estudiantes` (`id`);

--
-- Filtros para la tabla `estudiantes`
--
ALTER TABLE `estudiantes`
ADD CONSTRAINT `estudiantes_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`),
ADD CONSTRAINT `estudiantes_ibfk_2` FOREIGN KEY (`carrera_id`) REFERENCES `carreras` (`id`);

--
-- Filtros para la tabla `roles_por_usuario`
--
ALTER TABLE `roles_por_usuario`
ADD CONSTRAINT `roles_por_usuario_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`),
ADD CONSTRAINT `roles_por_usuario_ibfk_2` FOREIGN KEY (`rol_id`) REFERENCES `roles` (`id`);

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */
;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */
;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */
;